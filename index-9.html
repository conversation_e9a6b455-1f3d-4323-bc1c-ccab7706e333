<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from uigaint.com/demo/html/gittu-html/index-9.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 08 Jul 2025 08:55:48 GMT -->
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gittu - Home Nine</title>

  <!-- favicon included here -->
  <link rel="shortcut icon" href="assets/images/logo/favicon.ico" type="image/x-icon">
  <!-- apple touch icon included here -->
  <link rel="apple-touch-icon" href="assets/images/logo/favicon.ico">

  <!-- All css files included here -->
  <!-- Bootstrap v5.3.6 css -->
  <link rel="stylesheet" href="assets/css/bootstrap.min.css">
  <!-- custom css -->
  <link rel="stylesheet" href="assets/css/style.css">

  <style>
    /* Custom Swap Form Styles */
    .swap-form-container {
      padding: 20px 0;
    }

    .swap-form-section {
      margin-bottom: 16px;
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .section-label {
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
    }

    .chain-selector {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #94a3b8;
      font-size: 14px;
      cursor: pointer;
    }

    .chain-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
    }

    .token-input-container {
      background: rgba(30, 41, 59, 0.9);
      border: 1px solid rgba(71, 85, 105, 0.8);
      border-radius: 12px;
      padding: 16px;
      backdrop-filter: blur(10px);
    }

    .token-input-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .token-selector-left {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(51, 65, 85, 0.9);
      border: 1px solid rgba(71, 85, 105, 0.8);
      border-radius: 8px;
      padding: 10px 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 100px;
    }

    .token-selector-left:hover {
      background: rgba(51, 65, 85, 1);
      border-color: rgba(71, 85, 105, 1);
    }

    .token-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
    }

    .token-symbol {
      color: #ffffff;
      font-weight: 600;
      font-size: 14px;
    }

    .dropdown-arrow {
      color: #94a3b8;
      font-size: 10px;
    }

    .amount-input-section {
      flex: 1;
      text-align: right;
    }

    .amount-input {
      background: transparent;
      border: none;
      outline: none;
      color: #ffffff;
      font-size: 24px;
      font-weight: 600;
      text-align: right;
      width: 100%;
      max-width: 120px;
    }

    .amount-input::placeholder {
      color: rgba(148, 163, 184, 0.5);
    }

    .balance-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
    }

    .balance-info {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #94a3b8;
    }

    .balance-icon {
      font-size: 12px;
    }

    .balance-text {
      color: #94a3b8;
    }

    .max-btn {
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
      border: none;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .max-btn:hover {
      background: rgba(59, 130, 246, 0.3);
    }

    .usd-value {
      color: #94a3b8;
    }

    .swap-arrow-section {
      display: flex;
      justify-content: center;
      margin: 12px 0;
    }

    .swap-arrow-btn {
      background: rgba(30, 41, 59, 0.9);
      border: 2px solid rgba(71, 85, 105, 0.6);
      border-radius: 8px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #94a3b8;
    }

    .swap-arrow-btn:hover {
      background: rgba(30, 41, 59, 1);
      border-color: rgba(71, 85, 105, 0.8);
      transform: rotate(180deg);
    }

    .swap-arrow-btn svg {
      width: 16px;
      height: 16px;
    }

    .exchange-rate-info {
      background: rgba(30, 41, 59, 0.8);
      border: 1px solid rgba(71, 85, 105, 0.6);
      border-radius: 8px;
      padding: 12px;
      margin: 16px 0;
      font-size: 12px;
      text-align: center;
    }

    .rate-text {
      color: #94a3b8;
      display: block;
      margin-bottom: 6px;
    }

    .fee-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #94a3b8;
    }

    .info-icon {
      font-size: 12px;
      cursor: pointer;
    }

    .connect-wallet-btn-form {
      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
      border: none;
      border-radius: 12px;
      color: #ffffff;
      font-size: 14px;
      font-weight: 600;
      padding: 14px 24px;
      width: 100%;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-top: 16px;
    }

    .connect-wallet-btn-form:hover {
      background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
    }
  </style>
</head>

<body>
  <!-- Header Section Start -->
  <header class="header-section header9">
    <div class="container">
      <div class="header-inner">
        <a href="index.html" class="logo">
          <img src="assets/images/logo/logo.svg" alt="logo">
        </a>

        <div class="header-right">
          <ul class="social-links">
            <li>
              <a href="https://telegram.org/" target="_blank">
                <img src="assets/images/icon/telegram.svg" alt="icon">
              </a>
            </li>
            <li>
              <a href="https://discord.com/" target="_blank">
                <img src="assets/images/icon/discord.svg" alt="icon">
              </a>
            </li>
            <li>
              <a href="https://x.com/" target="_blank">
                <img src="assets/images/icon/twitter.svg" alt="icon">
              </a>
            </li>
          </ul>

          <button type="button" class="connect-wallet-btn v9">
            Connect <span>Wallet</span>
          </button>

          <div class="menu-btn">
            <button type="button" class="demo-btn">
              <img src="assets/images/icon/menu.svg" alt="icon">
            </button>

            <ul class="demo-list">
              <li><a href="index.html">Demo One</a></li>
              <li><a href="index-2.html">Demo Two</a></li>
              <li><a href="index-3.html">Demo Three</a></li>
              <li><a href="index-4.html">Demo Four</a></li>
              <li><a href="index-5.html">Demo Five</a></li>
              <li><a href="index-6.html">Demo Six</a></li>
              <li><a href="index-7.html">Demo Seven</a></li>
              <li><a href="index-8.html">Demo Eight</a></li>
              <li><a class="active" href="index-9.html">Demo Nine</a></li>
              <li><a href="index-10.html">Demo Ten</a></li>
            </ul>
          </div>

          <!-- mobile-menu -->
          <div class="mobile-menu">
            <button class="navbar-toggler mobile-menu-btn" type="button" data-bs-toggle="offcanvas"
              data-bs-target="#offcanvasNavbar" aria-controls="offcanvasNavbar" aria-label="Toggle navigation">
              <img src="assets/images/icon/menu-bar.svg" alt="icon">
            </button>

            <div class="offcanvas offcanvas-start mobile-menu-content" data-bs-scroll="true" tabindex="-1"
              id="offcanvasNavbar">
              <div class="offcanvas-header mobile-menu-top">
                <a href="index.html" class="logo">
                  <img src="assets/images/logo/logo.svg" alt="logo">
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close">
                  <img src="assets/images/icon/cross.svg" alt="icon">
                </button>
              </div>

              <div class="offcanvas-body">
                <ul class="mobile-menu-list">
                  <li><a href="index.html">Demo One</a></li>
                  <li><a href="index-2.html">Demo Two</a></li>
                  <li><a href="index-3.html">Demo Three</a></li>
                  <li><a href="index-4.html">Demo Four</a></li>
                  <li><a href="index-5.html">Demo Five</a></li>
                  <li><a href="index-6.html">Demo Six</a></li>
                  <li><a href="index-7.html">Demo Seven</a></li>
                  <li><a href="index-8.html">Demo Eight</a></li>
                  <li><a class="active" href="index-9.html">Demo Nine</a></li>
                  <li><a href="index-10.html">Demo Ten</a></li>
                </ul>

                <ul class="mobile-social-links">
                  <li>
                    <a href="https://telegram.org/" target="_blank">
                      <img src="assets/images/icon/telegram.svg" alt="">
                    </a>
                  </li>
                  <li>
                    <a href="https://discord.com/" target="_blank">
                      <img src="assets/images/icon/discord.svg" alt="">
                    </a>
                  </li>
                  <li>
                    <a href="https://x.com/" target="_blank">
                      <img src="assets/images/icon/twitter.svg" alt="">
                    </a>
                  </li>
                </ul>

                <ul class="mobile-menu-list">
                  <li>
                    <a class="whitepaper-btn-mobile" target="_blank" aria-current="page"
                      href="assets/pdf/whitepaper.pdf">
                      Whitepaper
                    </a>
                  </li>
                </ul>

                <div class="connect-btn">
                  <button type="button" class="connect-wallet-btn">
                    Connect <span>Wallet</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- Header Section End -->

  <section class="banner9-section">
    <div class="container">
      <div class="row">
        <div class="col-lg-6">
          <div class="banner9-left">
            <h1 class="uppercase kufam">Discover the next big Token</h1>
            <p class="inter">
              Invest in cutting-edge blockchain projects and be part of
              the future of web3.0 finance
            </p>
            <button>
              <span><img src="assets/images/icon/document-text.svg" alt="icon"></span>Whitepaper
            </button>
            <ul class="banner-list inter d-none d-lg-block">
              <li>Total Supply: <span>650,000,000</span></li>
              <li>Soft Cap: <span>50000</span></li>
              <li>Hard Cap: <span>100000</span></li>
            </ul>
          </div>
        </div>

        <div class="col-lg-6">
          <div class="banner9-right">
            <div class="card-shape">
              <div class="card-shape-inner">
                <img src="assets/images/shape/live.svg" alt="live">
                <img src="assets/images/icon/back-icon.svg" alt="icon" class="live-arrow">
              </div>
            </div>

            <div class="banner9-card">
              <div class="card-content">
                <p class="inter">Stage 1 : 50% Bonus !</p>
                <h5 class="outfit wt-600 uppercase">Pre-Sale Ends in</h5>
                <div class="banner9-timer">
                  <ul class="presale-end-time outfit">
                    <li class="days">00<span>d</span></li>
                    <li class="hours">00<span>h</span></li>
                    <li class="minutes">00<span>m</span></li>
                    <li class="seconds">00<span>s</span></li>
                  </ul>
                </div>

                <div class="card-progress-section">
                  <div class="card-progress-bar">
                    <div class="card-progress-inner inter">23%</div>
                  </div>
                  <h5 class="inter wt-500">
                    <span>Raised: 2,498,713.28 USD</span>
                    <span>Goal: 2,500,000 USD </span>
                  </h5>
                </div>

                <div class="card-info">
                  <ul class="inter">
                    <li><span>Token Name</span> <span>Uigigs</span></li>
                    <li>
                      <span>Token Symbol</span>
                      <span class="uppercase">Uigigs</span>
                    </li>
                    <li>
                      <span>Current Price</span> <span>$0.025</span>
                    </li>
                    <li>
                      <span>CEX Listings</span>
                      <span>$0.040 (25 Dec, 2023)</span>
                    </li>
                  </ul>
                </div>

                <button class="buy-btn flip-card-btn">Buy uigigs Now</button>
              </div>

              <div class="card-content2">
                <span class="back-btn">
                  <img src="assets/images/icon/back-icon.svg" alt="icon">
                </span>

                <div class="swap-form-container">
                  <!-- From Section -->
                  <div class="swap-form-section">
                    <div class="section-header">
                      <span class="section-label">From</span>
                      <div class="chain-selector">
                        <img src="assets/images/token/arbitrum-logo.png" alt="Arbitrum" class="chain-icon">
                        <span>Arbitrum</span>
                        <span class="dropdown-arrow">▼</span>
                      </div>
                    </div>

                    <div class="token-input-container">
                      <div class="token-input-row">
                        <div class="token-selector-left">
                          <img src="assets/images/token/eth_logo.png" alt="ETH" class="token-icon">
                          <span class="token-symbol">ETH</span>
                          <span class="dropdown-arrow">▼</span>
                        </div>
                        <div class="amount-input-section">
                          <input type="text" class="amount-input" placeholder="0" value="0">
                        </div>
                      </div>
                      <div class="balance-row">
                        <div class="balance-info">
                          <span class="balance-icon">⚖️</span>
                          <span class="balance-text">--</span>
                          <span class="max-btn">Max</span>
                        </div>
                        <div class="usd-value">$0.00</div>
                      </div>
                    </div>
                  </div>

                  <!-- Swap Arrow -->
                  <div class="swap-arrow-section">
                    <button class="swap-arrow-btn" type="button">
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 13L12 18L17 13M7 6L12 11L17 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                  </div>

                  <!-- To Section -->
                  <div class="swap-form-section">
                    <div class="section-header">
                      <span class="section-label">To</span>
                      <div class="chain-selector">
                        <img src="assets/images/token/arbitrum-logo.png" alt="Arbitrum" class="chain-icon">
                        <span>Arbitrum</span>
                        <span class="dropdown-arrow">▼</span>
                      </div>
                    </div>

                    <div class="token-input-container">
                      <div class="token-input-row">
                        <div class="token-selector-left">
                          <img src="assets/images/token/token_usdt.svg" alt="USDC" class="token-icon">
                          <span class="token-symbol">USDC</span>
                          <span class="dropdown-arrow">▼</span>
                        </div>
                        <div class="amount-input-section">
                          <input type="text" class="amount-input" placeholder="0" value="0" readonly>
                        </div>
                      </div>
                      <div class="balance-row">
                        <div class="balance-info">
                          <span class="balance-icon">⚖️</span>
                          <span class="balance-text">--</span>
                        </div>
                        <div class="usd-value">$0.00</div>
                      </div>
                    </div>
                  </div>

                  <!-- Exchange Rate -->
                  <div class="exchange-rate-info">
                    <span class="rate-text">1 ETH = 0.0000 USDC ($-)</span>
                    <div class="fee-info">
                      <span>Fee: ~ ETH</span>
                      <span class="info-icon">ℹ️</span>
                    </div>
                  </div>

                  <!-- Connect Wallet Button -->
                  <button class="connect-wallet-btn-form" type="button">
                    Connect Wallet
                  </button>
                </div>
              </div>
            </div>
          </div>

          <ul class="banner-list v2 inter d-block d-lg-none">
            <li>Total Supply: <span>650,000,000</span></li>
            <li>Soft Cap: <span>50000</span></li>
            <li>Hard Cap: <span>100000</span></li>
          </ul>
        </div>
      </div>
    </div>

    <div class="banner9-footer">
      <ul>
        <li><img src="assets/images/brands/1.svg" alt="icon"></li>
        <li><img src="assets/images/brands/2.svg" alt="icon"></li>
        <li><img src="assets/images/brands/3.svg" alt="icon"></li>
        <li><img src="assets/images/brands/4.svg" alt="icon"></li>
        <li><img src="assets/images/brands/5.svg" alt="icon"></li>
        <li><img src="assets/images/brands/6.svg" alt="icon"></li>
        <li><img src="assets/images/brands/7.svg" alt="icon"></li>
        <li><img src="assets/images/brands/8.svg" alt="icon"></li>
      </ul>
      <ul>
        <li><img src="assets/images/brands/1.svg" alt="icon"></li>
        <li><img src="assets/images/brands/2.svg" alt="icon"></li>
        <li><img src="assets/images/brands/3.svg" alt="icon"></li>
        <li><img src="assets/images/brands/4.svg" alt="icon"></li>
        <li><img src="assets/images/brands/5.svg" alt="icon"></li>
        <li><img src="assets/images/brands/6.svg" alt="icon"></li>
        <li><img src="assets/images/brands/7.svg" alt="icon"></li>
        <li><img src="assets/images/brands/8.svg" alt="icon"></li>
      </ul>
    </div>
  </section>

  <!-- connect modal -->
  <div class="connect-modal">
    <!-- connect modal -->
    <div class="modal fade" id="connectModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Connect a Wallet</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
              <img src="assets/images/icon/cross.svg" alt="icon">
            </button>
          </div>
          <div class="modal-body">
            <ul class="wallet-list">
              <li>
                <a class="gittu-connect-btn">
                  <img src="assets/images/wallet/metamask.svg" alt="metamask"><span
                    class="wt-700 kanit">MetaMask</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/formatic.png" alt="formatic">
                  <span class="wt-700 kanit">Formatic</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/trust-wallet.svg" alt="trust-wallet">
                  <span class="wt-700 kanit">Trust Wallet</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/wallet-connect.svg" alt="wallet-connect"><span
                    class="wt-700 kanit">WalletConnect</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/phantom.png" alt="phantom">
                  <span class="wt-700 kanit">Phantom</span>
                </a>
              </li>
            </ul>
            <p class="text-center">
              By connecting your wallet, you agree to our
              <a href="#">Terms of Service</a> and our
              <a href="#">Privacy Policy</a>.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- disconnect modal -->
    <div class="modal fade" id="disconnectModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Disconnect wallet</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
              <img src="assets/images/icon/cross.svg" alt="icon">
            </button>
          </div>
          <div class="modal-body">
            <div class="gittu-wallet-info">
              <h4 class="wallet-address">01ax...24544</h4>
              <h4><span class="wallet-balance">0 ETH</span></h4>
            </div>

            <div class="action-buttons">
              <button class="gittu-copywallet-btn">
                <img src="assets/images/icon/copy.svg" alt="metamask">
                <span>Copy Address</span>
              </button>
              <button class="gittu-disconnect-btn">
                <img src="assets/images/icon/disconnect.svg" alt="metamask">
                <span>Disconnect</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- All javascript files included here -->
  <!-- jQuery v3.7.1 js -->
  <script src="assets/js/jquery.min.js"></script>
  <!-- Bootstrap v5.3.6 js -->
  <script src="assets/js/bootstrap.min.js"></script>
  <!-- modernizr js -->
  <script src="assets/js/modernizr.js"></script>
  <!-- connect wallet js -->
  <script src="assets/js/ethers.umd.min.js"></script>
  <script src="assets/js/connectWallet.js"></script>
  <!-- custom js -->
  <script src="assets/js/main.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get form elements
      const fromAmountInput = document.querySelector('.swap-form-section:first-child .amount-input');
      const toAmountInput = document.querySelector('.swap-form-section:last-child .amount-input');
      const swapArrowBtn = document.querySelector('.swap-arrow-btn');
      const connectWalletBtn = document.querySelector('.connect-wallet-btn-form');
      const maxBtn = document.querySelector('.max-btn');

      // Mock exchange rate (1 ETH = 2500 USDC)
      const exchangeRate = 2500;

      // Handle amount input changes
      if (fromAmountInput) {
        fromAmountInput.addEventListener('input', function() {
          const amount = parseFloat(this.value) || 0;
          const convertedAmount = amount * exchangeRate;
          if (toAmountInput) {
            toAmountInput.value = convertedAmount.toFixed(4);
          }

          // Update USD values
          updateUSDValues();
        });
      }

      // Handle swap arrow click
      if (swapArrowBtn) {
        swapArrowBtn.addEventListener('click', function() {
          // Swap the token positions (visual effect)
          const fromSection = document.querySelector('.swap-form-section:first-child');
          const toSection = document.querySelector('.swap-form-section:last-child');

          if (fromSection && toSection) {
            // Get token selectors
            const fromTokenSelector = fromSection.querySelector('.token-selector-left');
            const toTokenSelector = toSection.querySelector('.token-selector-left');

            // Swap token icons and symbols
            const fromIcon = fromTokenSelector.querySelector('.token-icon').src;
            const fromSymbol = fromTokenSelector.querySelector('.token-symbol').textContent;
            const toIcon = toTokenSelector.querySelector('.token-icon').src;
            const toSymbol = toTokenSelector.querySelector('.token-symbol').textContent;

            fromTokenSelector.querySelector('.token-icon').src = toIcon;
            fromTokenSelector.querySelector('.token-symbol').textContent = toSymbol;
            toTokenSelector.querySelector('.token-icon').src = fromIcon;
            toTokenSelector.querySelector('.token-symbol').textContent = fromSymbol;

            // Swap amounts
            const fromAmount = fromAmountInput.value;
            const toAmount = toAmountInput.value;
            fromAmountInput.value = toAmount;
            toAmountInput.value = fromAmount;

            // Update labels
            const fromLabel = fromSection.querySelector('.section-label');
            const toLabel = toSection.querySelector('.section-label');
            const tempText = fromLabel.textContent;
            fromLabel.textContent = toLabel.textContent;
            toLabel.textContent = tempText;
          }
        });
      }

      // Handle Max button click
      if (maxBtn) {
        maxBtn.addEventListener('click', function() {
          // Set max amount (mock value)
          if (fromAmountInput) {
            fromAmountInput.value = '1.5';
            fromAmountInput.dispatchEvent(new Event('input'));
          }
        });
      }

      // Handle Connect Wallet button
      if (connectWalletBtn) {
        connectWalletBtn.addEventListener('click', function() {
          // In a real app, this would trigger wallet connection
          console.log('Connect wallet clicked');
          // You can integrate with existing wallet connection logic
        });
      }

      // Handle chain selector clicks
      const chainSelectors = document.querySelectorAll('.chain-selector');
      chainSelectors.forEach(selector => {
        selector.addEventListener('click', function() {
          console.log('Chain selector clicked');
          // In a real app, this would open chain selection modal
        });
      });

      // Handle token selector clicks
      const tokenSelectors = document.querySelectorAll('.token-selector-left');
      tokenSelectors.forEach(selector => {
        selector.addEventListener('click', function() {
          console.log('Token selector clicked');
          // In a real app, this would open token selection modal
        });
      });

      // Update USD values function
      function updateUSDValues() {
        const fromAmount = parseFloat(fromAmountInput?.value) || 0;
        const toAmount = parseFloat(toAmountInput?.value) || 0;

        // Mock ETH price = $2500
        const ethPrice = 2500;
        const usdcPrice = 1;

        const fromUSD = fromAmount * ethPrice;
        const toUSD = toAmount * usdcPrice;

        // Update USD display values
        const fromUSDElement = document.querySelector('.swap-form-section:first-child .usd-value');
        const toUSDElement = document.querySelector('.swap-form-section:last-child .usd-value');

        if (fromUSDElement) {
          fromUSDElement.textContent = `$${fromUSD.toFixed(2)}`;
        }
        if (toUSDElement) {
          toUSDElement.textContent = `$${toUSD.toFixed(2)}`;
        }

        // Update exchange rate display
        const rateText = document.querySelector('.rate-text');
        if (rateText && fromAmount > 0) {
          const rate = toAmount / fromAmount;
          rateText.textContent = `1 ETH = ${rate.toFixed(4)} USDC ($${ethPrice})`;
        }
      }

      // Initialize USD values
      updateUSDValues();
    });
  </script>
</body>


<!-- Mirrored from uigaint.com/demo/html/gittu-html/index-9.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 08 Jul 2025 08:55:59 GMT -->
</html>