@charset "UTF-8";
/*------------------------------------------------------------------------------------
Theme Name: Gittu
Author: uigigs
Author URL: https://themeforest.net/user/uigigs/portfolio
version: 1.0.0
------------------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------------
CSS table of content

01. Settings style
02. Base style
03. Component Style
04. Header style
05. Banner Style
06. Footer Section

------------------------------------------------------------------------------------*/
/*--------------------------------
01. Settings style
--------------------------------*/
/*-- font family included --*/
/*-- inter font family --*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700;800;900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@400..900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Kufam:ital,wght@0,400..900;1,400..900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue&amp;display=swap");
/*--------------------------------
02. Base style
--------------------------------*/
/*-- reset style start --*/
:root {
  --percent: 20;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

::-moz-selection {
  background-color: #65ea88;
  color: #111111;
}

::selection {
  background-color: #65ea88;
  color: #111111;
}

body {
  font-family: "Outfit", sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 200%;
  color: #ffffff;
  background: #0b0c12;
  overflow-x: hidden;
}

.outfit {
  font-family: "Outfit", sans-serif !important;
}

.orbitron {
  font-family: "Orbitron", serif !important;
}

.inter {
  font-family: "Inter", sans-serif !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: 0;
  color: #ffffff;
}

h1 {
  font-size: 60px;
  font-weight: 600;
  line-height: 80px;
}

h2 {
  font-size: 40px;
  line-height: 138%;
}

h3 {
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
}

h4 {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
}

h5 {
  font-size: 18px;
  font-weight: 700;
  line-height: 23.44px;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.wt-400 {
  font-weight: 400;
}

.wt-500 {
  font-weight: 500;
}

.wt-600 {
  font-weight: 600;
}

.wt-700 {
  font-weight: 700;
}

.text-18 {
  font-size: 18px;
}

.break-word {
  word-break: break-all;
}

.row-gap50 {
  row-gap: 50px;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

a,
.btn,
button {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}
a:hover,
.btn:hover,
button:hover {
  text-decoration: none;
}
a:focus,
.btn:focus,
button:focus {
  text-decoration: none;
  outline: none;
  border: none;
}

*:focus {
  outline: none;
}

button,
input[type=submit] {
  cursor: pointer;
}

button {
  border-radius: 0;
  border: 0;
  background-color: transparent;
}

.btn {
  margin: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

.custom-ul,
.custom-ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

img {
  max-width: 100%;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.uppercase {
  text-transform: uppercase;
}

.text-transform-none {
  text-transform: none;
}

.text-white {
  color: #ffffff;
}

/*-- custom bootstrap style start --*/
@media (min-width: 1400px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1190px;
  }
  .container-custom {
    max-width: 1290px;
  }
}
/*-- custom bootstrap style end --*/
/*-- color style --*/
/*-- bg color style start --*/
/*-- bg color style end --*/
/*-- Lib custom Start --*/
.row {
  margin: 0px -15px !important;
}

.col-xxl-1,
.col-xxl-2,
.col-xxl-3,
.col-xxl-4,
.col-xxl-5,
.col-xxl-6,
.col-xxl-7,
.col-xxl-8,
.col-xxl-9,
.col-xxl-10,
.col-xxl-11,
.col-xxl-12,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12 {
  padding: 0px 15px !important;
}

/*-- Lib custom End --*/
@media only screen and (max-width: 1199px) {
  h5 {
    font-size: 16px;
  }
}
/*-- reset style end --*/
.gittu-input {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
}
.gittu-input label {
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-weight: 600;
  line-height: 187.5%;
  text-transform: uppercase;
  margin-bottom: 8px;
}
.gittu-input input {
  border-radius: 30px;
  border: 2px solid rgba(255, 255, 255, 0.12);
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  padding: 10px 20px;
  color: #fff;
  font-family: Outfit;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 100%;
  width: 100%;
  height: 60px;
}
.gittu-input.v6 {
  margin-top: 10px;
}
.gittu-input.v6 input {
  border-radius: 10px;
}
.gittu-input.v7 input {
  border-radius: 0;
}

.token-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4%;
  margin-bottom: 3%;
}
.token-content ul {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}
.token-content .token-left ul {
  justify-content: flex-start;
  gap: 20px;
}
.token-content .token-left ul li a {
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 60px;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.token-content .token-left ul li a:hover {
  border: 2px solid rgba(255, 255, 255, 0.4);
}
.token-content .token-right .buy-token-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 180px;
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 162.5%;
  text-transform: uppercase;
  gap: 9px;
}
.token-content.v6 .token-left ul li a {
  border-radius: 10px;
}
.token-content.v6 .token-left ul li a.active, .token-content.v6 .token-left ul li a:hover {
  border: 2px solid rgba(29, 255, 150, 0.5);
  background: rgba(255, 255, 255, 0.15);
}
.token-content.v6 .token-right {
  width: 100%;
  max-width: 220px;
}
.token-content.v6 .token-right .buy-token-btn {
  border-radius: 10px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.05);
  min-width: 220px;
  justify-content: flex-start;
  padding: 5px 20px;
  position: relative;
}
.token-content.v7 .token-left ul li a {
  border-radius: 0px;
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
}
.token-content.v7 .token-left ul li a.active, .token-content.v7 .token-left ul li a:hover {
  border: 2px solid rgba(29, 255, 150, 0.5);
  background: rgba(255, 255, 255, 0.15);
}
.token-content.v7 .token-right {
  width: 100%;
  max-width: 220px;
}
.token-content.v7 .token-right .buy-token-btn {
  border-radius: 0px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.05);
  min-width: 220px;
  justify-content: flex-start;
  padding: 5px 20px;
  position: relative;
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
}
.token-content.v8 {
  margin-top: 15px;
  margin-bottom: 30px;
}
.token-content.v8 .token-left ul li a {
  border-radius: 60px;
  background: transparent;
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
  position: relative;
  box-sizing: border-box;
  border: none;
}
.token-content.v8 .token-left ul li a:after {
  content: "";
  position: absolute;
  top: 1px;
  right: 1px;
  bottom: 1px;
  left: 1px;
  z-index: -1; /* !importanté */
  border-radius: inherit; /* !importanté */
  background: #393b5a;
}
.token-content.v8 .token-left ul li a.active, .token-content.v8 .token-left ul li a:hover {
  background: rgba(255, 255, 255, 0.15);
}
.token-content.v8 .token-left ul li a.active:before, .token-content.v8 .token-left ul li a:hover:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1; /* !importanté */
  border-radius: inherit; /* !importanté */
  background: linear-gradient(to right, red, orange);
  background: linear-gradient(90deg, #3c38ff 0%, #8c45ff 33.33%, #ff36c7 68.23%, #ffa336 100%);
}
.token-content.v8 .token-right .buy-token-btn {
  border-radius: 60px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.05);
  min-width: 220px;
  justify-content: flex-start;
  padding: 5px 20px;
  position: relative;
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
}

@keyframes smoothSlider {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1200px);
  }
}
@keyframes rotate360 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.token-dropdown {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  border-radius: 10px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 18px;
  width: 100%;
  height: 60px;
  max-width: 220px;
  text-transform: uppercase;
  cursor: pointer;
}
.token-dropdown::after {
  top: 12px;
}
.token-dropdown.v6, .token-dropdown.v8, .token-dropdown.v9, .token-dropdown.v10 {
  max-width: 100%;
  text-transform: uppercase;
}
.token-dropdown.v6::after, .token-dropdown.v8::after, .token-dropdown.v9::after, .token-dropdown.v10::after {
  top: 12px;
}
.token-dropdown.v7 {
  border-radius: 0;
}
.token-dropdown::after {
  position: absolute;
  z-index: 0;
  content: url("../images/icon/dropdown.svg");
  right: 15px;
  top: 16px;
}
.token-dropdown .token-name {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  color: #fff;
  font-family: Outfit;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 166.667%;
  position: relative;
  z-index: 1;
  height: 60px;
  width: 100%;
}
.token-dropdown .token-drpdown-list {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  overflow: hidden;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  width: 100%;
  position: absolute;
  z-index: 11;
  top: 62px;
  left: 0px;
  display: none;
}
.token-dropdown .token-drpdown-list.show {
  display: block;
}
.token-dropdown .token-drpdown-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.token-dropdown .token-drpdown-list ul li {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  transition: 0.3s;
  width: 100%;
  color: #ffffff;
  cursor: pointer;
}
.token-dropdown .token-drpdown-list ul li:hover {
  background: rgba(255, 255, 255, 0.05);
}

@media only screen and (max-width: 1199px) {
  .token-dropdown .token-name {
    font-size: 16px;
  }
}
@media only screen and (max-width: 768px) {
  .token-content.v4, .token-content.v5 {
    position: relative;
    padding-top: 60px;
  }
  .token-content.v4 .token-center, .token-content.v5 .token-center {
    position: absolute;
    width: 100%;
    top: 0;
  }
}
@media only screen and (max-width: 575px) {
  .buy-btn {
    margin-top: 10px !important;
  }
  .token-content.v3 {
    margin-top: 20px;
  }
  .token-content.v6, .token-content.v7, .token-content.v8 {
    flex-wrap: wrap;
    gap: 20px;
  }
  .token-content.v6 .token-right, .token-content.v7 .token-right, .token-content.v8 .token-right {
    width: 100%;
  }
  .token-content.v6 .token-right .buy-token-btn, .token-content.v7 .token-right .buy-token-btn, .token-content.v8 .token-right .buy-token-btn {
    width: 100%;
  }
  .token-dropdown.v10 {
    max-width: 100%;
  }
  .gittu-input {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 375px) {
  .token-content.v3, .token-content.v4, .token-content.v5 {
    flex-wrap: wrap;
    gap: 20px;
  }
  .token-content.v3 .token-left,
  .token-content.v3 .token-right, .token-content.v4 .token-left,
  .token-content.v4 .token-right, .token-content.v5 .token-left,
  .token-content.v5 .token-right {
    width: 100%;
  }
}
/*--------------------------------
03. Component Style
--------------------------------*/
.buy-modal .modal-content {
  width: 530px;
  max-width: 100%;
  padding: 40px;
}
.buy-modal .modal-header {
  padding: 0;
  margin-bottom: 25px;
}
.buy-modal .btn-close {
  transition: 0.3s;
}
.buy-modal .btn-close:focus {
  box-shadow: none;
}
.buy-modal .modal-body {
  padding: 0;
}
.buy-modal .token-list {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
.buy-modal .token-list li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 9px 0px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
  color: #ffffff;
}
.buy-modal h5 {
  margin-bottom: 22px;
}
.buy-modal .approve-btn {
  width: 100%;
  margin-top: 40px;
  background: linear-gradient(90deg, #e05fbb 0%, #4250f4 100%);
  border-radius: 50px;
  height: 60px;
  font-weight: 600;
  color: #ffffff;
}
.buy-modal .approve-btn.v2 {
  border-radius: 30px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  color: #111;
}

.connect-modal .modal-content {
  max-width: 368px;
}
.connect-modal .modal-header {
  justify-content: center;
}
.connect-modal .modal-header .modal-title {
  text-transform: uppercase;
  font-size: 20px;
  line-height: 24px;
}
.connect-modal .modal-header .btn-close {
  position: absolute;
  right: 20px;
  top: 26px;
  padding: 6px;
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.04);
  background-color: rgba(255, 255, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.connect-modal .modal-header .btn-close img {
  width: 12px;
  height: 12px;
  transition: 0.3s;
}
.connect-modal .modal-header .btn-close:hover {
  background-color: rgba(255, 255, 255, 0.15);
}
.connect-modal .modal-header .btn-close:focus {
  box-shadow: none;
}
.connect-modal .modal-body p {
  text-align: center;
  max-width: 300px;
  margin: auto;
  margin-top: -15px;
  margin-bottom: 20px;
  color: #fff;
}
.connect-modal .modal-body p a {
  font-weight: 700;
}
.connect-modal .modal-body p a:hover {
  color: #fff;
}
.connect-modal .wallet-list {
  list-style: none;
  padding: 0px;
  margin: 0px;
  padding-bottom: 20px;
}
.connect-modal .wallet-list li a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  background: transparent;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border-radius: 10px;
  width: 100%;
  height: 50px;
  cursor: pointer;
  position: relative;
  padding: 10px;
  color: rgba(255, 255, 255, 0.8);
  transition: 0.3s;
}
.connect-modal .wallet-list li a::after {
  display: none;
  position: absolute;
  content: "";
  right: 30px;
  top: 20px;
  width: 12px;
  height: 12px;
  background-image: url("../images/icon/arrow-right.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.connect-modal .wallet-list li a img {
  width: 30px;
  height: 30px;
}
.connect-modal .wallet-list li a span {
  font-size: 16px;
  line-height: 30px;
}
.connect-modal .wallet-list li a:hover {
  background: #2e3036;
  color: #ffffff;
}
.connect-modal .modal-content::after {
  left: -50px;
}

.modal-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: #1a1b1f;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}
.modal-content.token-popup::after {
  content: url(../images/shape/modal-shape.png);
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 0;
}
.modal-content.token-popup.v2::after {
  content: url(../images/shape/modal-shape-v2.png);
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 0;
}
.modal-content .btn-close {
  background: none;
  margin-right: 0px;
  margin-bottom: 0px;
  opacity: 1;
}
.modal-content .btn-close svg {
  color: #ffffff;
  font-size: 26px;
}

.modal-header {
  border: none;
  position: relative;
  z-index: 1;
}

.modal-body {
  position: relative;
  z-index: 1;
}

.gittu-wallet-info {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 0px;
}
.gittu-wallet-info h4 {
  margin-bottom: 0;
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  color: white;
}
.gittu-wallet-info h4 span {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}
.action-buttons button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  gap: 2px;
  background: rgba(224, 232, 255, 0.1);
  border-radius: 10px;
  width: 100%;
  cursor: pointer;
  position: relative;
  padding: 9px;
  color: rgba(255, 255, 255, 0.8);
  transition: 0.3s;
}
.action-buttons button img {
  width: 14px;
  height: 14px;
  transition: 0.3s;
}
.action-buttons button span {
  font-size: 15px;
  line-height: 26px;
  transition: 0.3s;
}
.action-buttons button:hover {
  background: rgba(224, 232, 255, 0.2);
  color: #ffffff;
  transform: scale(1.025);
}

@media only screen and (max-width: 575px) {
  .buy-modal .modal-content {
    padding: 20px;
  }
}
.presale-end-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 25px;
}
.presale-end-time li {
  display: flex;
  align-items: baseline;
  position: relative;
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.presale-end-time li:not(:last-child) {
  padding-right: 25px;
}
.presale-end-time li:not(:last-child)::after {
  position: absolute;
  content: ":";
  top: 0px;
  right: -5px;
  font-size: 40px;
  font-weight: 700;
  line-height: 60px;
  color: rgba(255, 255, 255, 0.2);
}
.presale-end-time li span {
  font-weight: 700;
  font-size: 24px;
  color: #ffffff;
}

@media only screen and (max-width: 1199px) {
  .presale-end-time {
    gap: 15px;
  }
  .presale-end-time li {
    font-size: 30px;
    line-height: 50px;
  }
  .presale-end-time li:not(:last-child) {
    padding-right: 15px;
  }
  .presale-end-time li:not(:last-child)::after {
    font-size: 30px;
    line-height: 50px;
  }
  .presale-end-time li span {
    font-size: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .presale-end-time {
    gap: 10px;
  }
  .presale-end-time li {
    font-size: 22px;
    line-height: 45px;
  }
  .presale-end-time li:not(:last-child) {
    padding-right: 10px;
  }
  .presale-end-time li:not(:last-child)::after {
    top: 0px;
    right: -3px;
    font-size: 22px;
    line-height: 45px;
  }
  .presale-end-time li span {
    font-size: 16px;
  }
}
/*--------------------------------
04. Header style
--------------------------------*/
.header-section {
  padding: 25px 0px;
  position: absolute;
  width: 100%;
  left: 0px;
  top: 0px;
  z-index: 111;
}
.header-section.v10 {
  padding-top: 13px;
}

.header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-inner .social-links {
  display: flex;
  align-items: center;
  gap: 20px;
}
.header-inner .social-links li a {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 50px;
  width: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.header-inner .social-links li a img {
  width: 20px;
  transition: 0.3s;
}
.header-inner .social-links li a:hover {
  opacity: 0.7;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 40%;
}
.header-right.v2, .header-right.v3, .header-right.v4, .header-right.v6, .header-right.v8, .header-right.v9 {
  width: 70%;
}
.header-right .header-link {
  line-height: 26px;
  font-weight: 700;
  text-transform: uppercase;
}
.header-right .header-link:hover {
  color: #ffffff;
}

.connect-wallet-btn {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 12px 24px;
  line-height: 26px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  gap: 8px;
}
.connect-wallet-btn .short-address {
  display: none;
}
.connect-wallet-btn img {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
.connect-wallet-btn.v3 {
  color: #111;
  background: #07e6f5;
}
.connect-wallet-btn.v3 img {
  filter: brightness(0);
}
.connect-wallet-btn.v4 {
  color: #111;
  background: #fcd930;
}
.connect-wallet-btn.v4 img {
  filter: brightness(0);
}
.connect-wallet-btn.v5 {
  color: #111;
  border-radius: 25px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
}
.connect-wallet-btn.v5 img {
  filter: brightness(0);
}
.connect-wallet-btn.v6 {
  color: #111;
  border-radius: 25px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
}
.connect-wallet-btn.v6 img {
  filter: brightness(0);
}
.connect-wallet-btn.v8 {
  color: #fff;
  background: linear-gradient(90deg, #3c38ff 0%, #7838ff 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.header-center {
  width: 20%;
  margin: auto;
  text-align: center;
}

.header-left {
  width: 40%;
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.menu-btn {
  position: relative;
}
.menu-btn.v7 .demo-btn {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
  border-radius: 0;
}
.menu-btn:hover .demo-list {
  visibility: visible;
  opacity: 1;
  transform: scale(1) translateY(0px);
}

.demo-btn {
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(7.5px);
          backdrop-filter: blur(7.5px);
  height: 50px;
  width: 50px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.demo-btn img {
  width: 22px;
  height: 22px;
}

.demo-list {
  position: absolute;
  top: calc(100% + 10px);
  right: 0px;
  width: 170px;
  border-radius: 10px;
  background: #1d1f2b;
  padding: 10px 0;
  transition: 0.3s;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.75) translateY(-21px);
}
.demo-list li a {
  font-size: 16px;
  width: 100%;
  background: transparent;
  padding: 4px 20px;
  display: flex;
  transition: 0.3s;
}
.demo-list li a:hover, .demo-list li a.active {
  color: #65ea88;
}

@media only screen and (max-width: 991px) {
  .header-inner .social-links {
    display: none;
  }
  .header-link {
    display: none;
  }
  .header-left {
    display: none;
  }
  .header-center {
    margin: 0;
    margin-right: auto;
    text-align: left;
  }
  .menu-btn.v7 {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .connect-wallet-btn {
    padding: 10px 20px;
  }
  .connect-wallet-btn span {
    display: none;
  }
  .connect-wallet-btn .short-address {
    display: block;
  }
}
@media only screen and (max-width: 480px) {
  .header-inner .logo {
    max-width: 100px;
  }
  .menu-btn {
    display: none;
  }
}
.mobile-menu {
  display: none;
}
.mobile-menu .navbar-toggler svg {
  font-size: 26px;
}
.mobile-menu .offcanvas-backdrop {
  background-color: rgba(4, 12, 18, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: 0.3s;
}
.mobile-menu .offcanvas-backdrop.fade {
  transition: 0.3s;
}
.mobile-menu .offcanvas-backdrop.show {
  opacity: 1;
}

.mobile-menu-content {
  background: #242631;
}
.mobile-menu-content .offcanvas-body {
  padding-top: 60px;
}
.mobile-menu-content .connect-btn {
  display: flex;
  align-content: center;
  justify-content: center;
}

.mobile-menu-top {
  padding: 30px;
}
.mobile-menu-top .logo img {
  max-width: 100px;
}
.mobile-menu-top .btn-close {
  width: 20px;
  height: 20px;
  background: transparent;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transition: 0.3s;
}
.mobile-menu-top .btn-close img {
  width: 14px;
}
.mobile-menu-top .btn-close svg {
  color: #ffffff;
  font-size: 32px;
}
.mobile-menu-top .btn-close:focus {
  box-shadow: none;
}

.mobile-menu-list {
  margin-bottom: 40px;
  text-align: center;
}
.mobile-menu-list li a {
  text-transform: capitalize;
  font-size: 18px;
  font-weight: 500;
  line-height: 40px;
  color: rgb(255, 255, 255);
  transition: 0.3s;
}
.mobile-menu-list li a:hover, .mobile-menu-list li a.active {
  color: #1ee8b7;
}

.mobile-social-links {
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}
.mobile-social-links li a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  transition: 0.3s;
}
.mobile-social-links li a img {
  width: 18px;
  transition: 0.3s;
}
.mobile-social-links li a:hover {
  opacity: 0.7;
}

.menu-social {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px;
  list-style: none;
  max-width: 190px;
  width: 100%;
  margin: auto;
  margin-bottom: 0px;
}
.menu-social li {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 25px;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}
.menu-social li:nth-last-child(1) {
  margin-right: 0px;
}
.menu-social li.no-bg {
  background: transparent;
}

.mobile-menu-btn {
  width: 30px;
  height: 30px;
}
.mobile-menu-btn img {
  width: 100%;
  height: 100%;
}

@media only screen and (max-width: 991px) {
  .mobile-menu {
    display: block;
  }
}
@media only screen and (max-width: 480px) {
  .mobile-menu-content .offcanvas-body {
    padding-top: 30px;
  }
  .mobile-menu-top {
    padding: 25px;
  }
}
/*--------------------------------
05. Banner Style
--------------------------------*/
.banner1-section {
  background: #0b0c12;
  min-height: 100vh;
  padding: 168px 0 40px 0;
  position: relative;
  z-index: 0;
}
.banner1-section::before {
  z-index: -1;
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/bg/mesh-grad-1.png");
  background-repeat: no-repeat;
  background-position: top center;
}
.banner1-section::after {
  z-index: -1;
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/bg/mesh-grad-2.png");
  background-repeat: no-repeat;
  background-position: bottom center;
}

.banner1-content h5 {
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
  line-height: 167%;
  text-align: center;
  color: #ffffff;
}
.banner1-content .banner1-timer {
  margin-bottom: 10px;
}
.banner1-content h2 {
  max-width: 978px;
  margin: auto;
  margin-bottom: 23px;
  font-size: 60px;
  font-weight: 600;
  line-height: 80px;
}
.banner1-content .price-text {
  text-align: center;
  margin-bottom: 31px;
}
.banner1-content .banner1-btn {
  max-width: 270px;
  width: 100%;
  height: 60px;
  background: linear-gradient(90deg, #e05fbb 0%, #4250f4 100%);
  border-radius: 50px;
  position: relative;
  z-index: 4;
}

.progress-section {
  max-width: 770px;
  width: 100%;
  margin: auto;
  margin-bottom: 1.5%;
  margin-top: 2%;
}
.progress-section .progress-top-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.progress-section .progress-top-text p {
  margin-bottom: 11px;
  font-weight: 600;
  font-size: 15px;
  line-height: 30px;
  text-transform: uppercase;
  color: #ffffff;
}
.progress-section .progress-bar-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px;
}
.progress-section .progress-bar-container .progress-bar-inner {
  background: #4250f4;
  border-radius: 12px;
  padding: 4px 6px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 23%;
}
.progress-section .progress-bar-container .progress-bar-inner span {
  font-weight: 700;
  font-size: 12px;
  line-height: 125%;
  text-align: right;
  color: #ffffff;
}

.banner1-social {
  position: relative;
  bottom: 0px;
  left: 0px;
  width: 100%;
  margin: auto;
  padding-top: 4%;
  padding-bottom: 2%;
  z-index: 3;
}
.banner1-social ul {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px;
  list-style: none;
  max-width: 520px;
  margin: auto;
}
.banner1-social ul li {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 25px;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-section .input-dwopdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  padding: 0;
  height: 60px;
  margin-bottom: 30px;
}
.input-section .input-dwopdown input {
  border: none;
  outline: none;
  width: 65%;
  background: transparent;
  margin-bottom: 0;
}
.input-section .input-dwopdown .token-dropdown {
  width: 160px;
  border: none;
  outline: none;
  position: relative;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 0px 10px 10px 0px;
}

@media only screen and (max-width: 1199px) {
  .banner1-content h2 {
    font-size: 55px;
    max-width: 660px;
    margin: auto;
    margin-bottom: 23px;
  }
}
@media only screen and (max-width: 768px) {
  .progress-section.v1 .progress-top-text {
    gap: 15px;
    row-gap: 0;
  }
  .banner1-social {
    bottom: 20px;
  }
  .banner1-content h2 {
    font-size: 40px;
    line-height: 150%;
  }
}
@media only screen and (max-width: 575px) {
  .banner1-section {
    padding: 120px 0 40px 0;
  }
  .banner1-content h2 {
    font-size: 24px;
  }
  .banner1-content h5 {
    margin-bottom: 20px;
  }
  .banner1-content .price-text {
    margin-top: 20px;
  }
  .progress-section .progress-top-text {
    flex-wrap: wrap;
  }
  .banner1-social {
    position: relative;
    padding-top: 50px;
    padding-bottom: 0px;
  }
}
@media only screen and (max-width: 425px) {
  .banner1-social ul li {
    height: 36px;
    padding: 10px;
    width: 36px;
  }
}
.container-home2 {
  max-width: 1720px;
  padding: 0px 20px;
  margin: auto;
}

.banner2-section {
  background: #0b0c12;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 0;
}
.banner2-section::before {
  z-index: -1;
  position: absolute;
  content: url("../images/bg/mesh-grad-3.png");
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.banner2-section::after {
  z-index: -1;
  position: absolute;
  content: url("../images/bg/banner2-bg-dots.png");
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.banner2-container {
  max-width: 1800px;
  width: calc(100% - 20px);
  margin-left: auto;
  margin-right: 0;
}

.banner2-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.banner2-row > * {
  max-width: 100%;
}

.banner2-col-left {
  flex: 0 0 auto;
  width: 44%;
  padding-top: 75px;
}

.banner2-col-right {
  flex: 0 0 auto;
  width: 47%;
}

.banner2-left h2 {
  color: #fff;
  font-size: 60px;
  font-weight: 900;
  line-height: 150%;
  text-transform: uppercase;
  margin-bottom: 3%;
}
.banner2-left p {
  font-size: 18px;
  margin-bottom: 6%;
}
.banner2-left h5 {
  font-size: 16px;
  font-weight: 500;
  line-height: 187.5%;
}

.progress-section-v2 {
  max-width: 770px;
  width: 100%;
  margin-right: auto;
  margin-bottom: 6%;
}
.progress-section-v2 .progress-top-text {
  margin-bottom: 11px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  flex-wrap: wrap;
}
.progress-section-v2 .progress-top-text p {
  margin-bottom: 0;
  font-weight: 600;
  font-size: 15px;
  line-height: 30px;
  text-transform: uppercase;
  color: #ffffff;
}
.progress-section-v2 .progress-bar-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px;
}
.progress-section-v2 .progress-bar-container .progress-bar-inner {
  background: #1dff96;
  border-radius: 12px;
  padding: 4px 6px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 23%;
}
.progress-section-v2 .progress-bar-container .progress-bar-inner span {
  font-weight: 700;
  font-size: 12px;
  line-height: 125%;
  text-align: right;
  color: #111;
}

.banner2-btn {
  border-radius: 30px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 270px;
  margin-top: 5%;
  color: #111111;
}

.banner2-right {
  position: relative;
}

.banner2-right {
  position: relative;
}
.banner2-right-img {
  width: 100%;
  height: 100vh;
  max-height: 100%;
  position: relative;
}
.banner2-right-img img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100vh;
  max-height: 100%;
}
.banner2-right .overlay-img {
  position: absolute;
  top: 25%;
  left: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.banner2-right .overlay-img img {
  width: 106px;
  height: 129px;
  animation: coinAnimate 10s infinite ease-in-out;
}

@keyframes coinAnimate {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  20% {
    transform: translateY(10px) rotate(0deg);
  }
  40% {
    transform: translateY(0px) rotate(0deg);
  }
  60% {
    transform: translateY(0px) rotate(360deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}
.presale-card {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 40px 120px;
  background: rgba(255, 255, 255, 0.1019607843);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.presale-card-header {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.presale-card h5 {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  line-height: 166.667%;
  margin-bottom: 25px;
}
.presale-card .timer {
  display: flex;
  align-items: center;
  gap: 60px;
}
.presale-card .timer li {
  color: #fff;
  font-family: Orbitron;
  font-size: 40px;
  font-weight: 900;
  line-height: 125%;
  position: relative;
}
.presale-card .timer li::after {
  content: ":";
  position: absolute;
  right: -35px;
  color: rgba(255, 255, 255, 0.4);
}
.presale-card .timer li span {
  font-size: 24px;
}
.presale-card .timer li:nth-last-child(1)::after {
  display: none;
}

.presale-card .social-links {
  margin-top: 100px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  flex-wrap: wrap;
}
.presale-card .social-links li a {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1490196078);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.presale-card .social-links li a img {
  transition: 0.3s;
}
.presale-card .social-links li a:hover {
  opacity: 0.7;
}

@media screen and (max-width: 1850px) {
  .banner2-container {
    max-width: 1750px;
  }
}
@media screen and (max-width: 1780px) {
  .banner2-container {
    max-width: 1720px;
  }
  .banner2-row {
    gap: 50px;
  }
  .banner2-col-left {
    width: 48%;
  }
  .banner2-col-right {
    width: 48%;
  }
}
@media screen and (max-width: 1899px) {
  .banner2-container {
    max-width: 1780px;
  }
}
@media screen and (max-width: 1699px) {
  .banner2-container {
    max-width: 1640px;
  }
}
@media screen and (max-width: 1599px) {
  .banner2-container {
    max-width: 1540px;
  }
}
@media screen and (max-width: 1499px) {
  .banner2-container {
    max-width: 1430px;
  }
  .presale-card {
    padding: 40px 80px;
  }
}
@media screen and (max-width: 1380px) {
  .banner2-container {
    max-width: 100%;
    width: 100%;
    margin: 0;
  }
  .banner2-row {
    width: 100%;
    display: flex;
    gap: 0px;
  }
  .banner2-col-left {
    width: 50%;
  }
  .banner2-col-right {
    width: 50%;
  }
  .banner2-left {
    padding: 0 20px;
  }
  .banner2-left h2 {
    gap: 10px;
    font-size: 38px;
    line-height: 60px;
  }
  .presale-card {
    padding: 30px 20px;
  }
  .presale-card .social-links {
    margin-top: 100px;
    gap: 15px;
  }
  .presale-card .social-links li a {
    width: 45px;
    height: 45px;
  }
}
@media screen and (max-width: 1199px) {
  .presale-card .timer {
    gap: 40px;
  }
  .presale-card .timer li {
    font-size: 30px;
    line-height: 50px;
  }
  .presale-card .timer li::after {
    right: -25px;
  }
}
@media screen and (max-width: 991px) {
  .banner2-section {
    padding-top: 150px;
  }
  .banner2-row {
    width: 100%;
    display: flex;
    gap: 50px;
  }
  .banner2-col-left {
    width: 100%;
    padding-top: 0px;
  }
  .banner2-col-right {
    width: 100%;
  }
  .banner2-left {
    padding: 0 20px;
  }
  .banner2-right-img {
    height: 100%;
  }
  .banner2-right-img img {
    height: 100%;
  }
  .presale-card-header {
    align-items: center;
  }
  .presale-card .social-links {
    margin-top: 60px;
    justify-content: center;
  }
  .banner2-left h2 {
    font-size: 30px;
    line-height: 50px;
  }
  .banner-body {
    max-width: 525px;
  }
}
@media screen and (max-width: 767px) {
  .presale-card .social-links {
    margin-top: 40px;
  }
}
@media screen and (max-width: 480px) {
  .banner2-section {
    padding-top: 120px;
  }
}
@media screen and (max-width: 480px) {
  .presale-card .timer {
    gap: 20px;
  }
  .presale-card .timer li {
    font-size: 22px;
    line-height: 40px;
  }
  .presale-card .timer li::after {
    right: -14px;
  }
}
.banner3-section {
  padding: 160px 0 100px 0;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 0;
  background-image: url("../images/bg/banner3-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.banner3-content {
  max-width: 770px;
  margin: auto;
}
.banner3-content h6 {
  margin-bottom: 2%;
  font-size: 18px;
}
.banner3-content h1 {
  margin-top: 3%;
  margin-bottom: 2%;
  color: #fff;
  text-align: center;
  font-family: Kufam;
  font-size: 70px;
  font-style: normal;
  font-weight: 700;
  line-height: 128.571%;
}
.banner3-content h1 span {
  background: linear-gradient(180deg, #fff 0%, #07e6f5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner3-content p {
  color: #fff;
  text-align: center;
  font-family: Outfit;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 166.667%;
  margin-bottom: 4%;
}
.banner3-content .buy-btn {
  margin-top: 3%;
}
.banner3-content .buy-btn.v3 {
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 50px;
  background: linear-gradient(90deg, #07e6f5 0%, #fc0cdf 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.timer-v3 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  list-style: none;
  padding: 0;
  margin: 0;
}
.timer-v3 li {
  color: #fff;
  text-align: center;
  font-family: Kufam;
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
  line-height: 125%;
  position: relative;
}
.timer-v3 li span {
  font-size: 24px;
}
.timer-v3 li::after {
  content: ":";
  position: absolute;
  right: -25px;
  color: rgba(255, 255, 255, 0.4);
}
.timer-v3 li:nth-last-child(1)::after {
  display: none;
}

.card-progress-section {
  margin-bottom: 2%;
}
.card-progress-section.v3 h5 {
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-weight: 600;
  line-height: 187.5%;
  text-transform: uppercase;
}
.card-progress-section.v3 h5 span {
  margin-bottom: 0;
}
.card-progress-section.v3 .card-progress-bar {
  margin-bottom: 1%;
  border-radius: 20px;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  padding: 8px;
}
.card-progress-section.v3 .card-progress-bar .card-progress-inner {
  height: 24px;
  border-radius: 20px;
}

.token-content.v3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 3%;
  margin-bottom: 3%;
}
.token-content.v3 ul {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}
.token-content.v3 .token-left ul {
  justify-content: flex-start;
  gap: 20px;
}
.token-content.v3 .token-left ul li a {
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 60px;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.token-content.v3 .token-left ul li a:hover {
  border: 2px solid rgba(255, 255, 255, 0.4);
}
.token-content.v3 .token-right .buy-token-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 180px;
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 162.5%;
  text-transform: uppercase;
  gap: 9px;
}

@media only screen and (max-width: 768px) {
  .banner3-content h1 {
    font-size: 50px;
  }
  .timer-v3 li {
    font-size: 30px;
  }
}
@media only screen and (max-width: 575px) {
  .banner3-section {
    padding: 120px 0 100px 0;
  }
  .banner3-content h1 {
    font-size: 36px;
  }
  .card-progress-section.v3 h5 {
    flex-direction: column;
  }
}
.banner4-section {
  padding: 200px 0 100px 0;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 0;
  background-image: url("../images/bg/banner4-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.banner4-content {
  max-width: 770px;
  margin: auto;
}

.banner4-header {
  margin-bottom: 40px;
}
.banner4-header h1 {
  color: #fff;
  text-align: center;
  text-shadow: 4px 6px 0px #fcd930;
  -webkit-text-stroke-width: 1;
  -webkit-text-stroke-color: #000;
  font-family: Kufam;
  font-size: 90px;
  font-style: normal;
  font-weight: 700;
  line-height: 100%;
}
.banner4-header p {
  margin-top: 1.5%;
  margin-bottom: 3%;
}

.buy-btn {
  margin-top: 3%;
}
.buy-btn.v4 {
  color: #111;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 50px;
  background: #fcd930;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.timer-v4 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  list-style: none;
  padding: 0;
  margin: 0;
}
.timer-v4 li {
  color: #fff;
  text-align: center;
  font-family: Kufam;
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
  line-height: 125%;
  position: relative;
}
.timer-v4 li span {
  font-size: 24px;
}
.timer-v4 li::after {
  content: ":";
  position: absolute;
  right: -25px;
  color: rgba(255, 255, 255, 0.4);
}
.timer-v4 li:nth-last-child(1)::after {
  display: none;
}

.semicircle-progress .progress-item {
  margin: 0 auto;
  width: 400px;
  height: 200px;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  box-sizing: border-box;
  overflow: hidden;
}
.semicircle-progress .progress-item::after {
  position: absolute;
  content: "";
  box-sizing: border-box;
  top: 0;
  left: 0;
  width: 400px;
  height: 400px;
  border-width: 30px;
  border-style: solid;
  border-top-color: rgba(255, 255, 255, 0.1019607843);
  border-right-color: rgba(255, 255, 255, 0.1019607843);
  border-bottom-color: #fcd930;
  border-left-color: #fcd930;
  border-radius: 50%;
  transform: rotate(calc(1deg * (-45 + var(--percent) * 1.8)));
}
.semicircle-progress .needle {
  position: absolute;
  top: 0;
  left: 0;
  width: 400px;
  height: 400px;
  transform: rotate(calc(1deg * (90 + var(--percent) * 1.8)));
}
.semicircle-progress .needle::before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 100%;
  background: linear-gradient(45deg, #ffffff 0%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0) 50%);
}
.semicircle-progress .progress-title {
  position: relative;
  z-index: 0;
  color: #ffffff;
  text-align: center;
  text-shadow: 2px 3px 0px #fcd930;
  -webkit-text-stroke-width: 1;
  -webkit-text-stroke-color: #000000;
  font-family: Kufam;
  font-size: 50px;
  font-style: normal;
  font-weight: 700;
  line-height: 1;
}
.semicircle-progress .status-content {
  margin-top: -48px;
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
}
.semicircle-progress .status-item.right {
  text-align: right;
}
.semicircle-progress .status-item h4 {
  color: #ffffff;
  font-family: Outfit;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  text-transform: uppercase;
}

.token-content.v4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4%;
  margin-bottom: 3%;
}
.token-content.v4 ul {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}
.token-content.v4 .token-left ul {
  justify-content: flex-start;
  gap: 20px;
}
.token-content.v4 .token-left ul li a {
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 60px;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.token-content.v4 .token-left ul li a:hover {
  border: 2px solid rgba(255, 255, 255, 0.4);
}
.token-content.v4 .token-right .buy-token-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  height: 60px;
  width: 180px;
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 162.5%;
  text-transform: uppercase;
  gap: 9px;
}

@media only screen and (max-width: 991px) {
  .banner4-content h1 {
    font-size: 70px;
  }
  .semicircle-progress .status-content {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 768px) {
  .banner4-content h1 {
    font-size: 60px;
  }
  .banner4-statistics {
    margin-top: 0;
  }
}
@media only screen and (max-width: 575px) {
  .banner4-section {
    padding: 150px 0 100px 0;
  }
  .banner4-content h1 {
    font-size: 40px;
    text-shadow: 2px 3px 0px #fcd930;
  }
  .timer-v4 li {
    font-size: 30px;
  }
  .banner4-statistics {
    margin-top: 0;
  }
}
@media only screen and (max-width: 480px) {
  .semicircle-progress .progress-item {
    width: 280px;
    height: 140px;
  }
  .semicircle-progress .progress-item::after {
    width: 280px;
    height: 280px;
  }
  .semicircle-progress .needle {
    width: 280px;
    height: 280px;
  }
  .semicircle-progress .progress-title {
    font-size: 30px;
  }
}
.banner5-section {
  background: url("../images/bg/banner5-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  padding: 223px 0 0px 0;
  position: relative;
  z-index: 0;
  overflow-x: hidden;
}

.banner5-content {
  max-width: 742px;
  margin: auto;
  position: relative;
  z-index: 2;
}
.banner5-content h1 {
  color: #fff;
  font-family: "Bebas Neue";
  font-size: 90px;
  font-style: normal;
  font-weight: 400;
  line-height: 90px;
  /* 100% */
}
.banner5-content p {
  font-size: 18px;
}

.card-progress-section.v5 {
  margin-bottom: 0;
  padding-bottom: 5px;
}
.card-progress-section.v5 h5 span {
  margin-bottom: 0;
}
.card-progress-section.v5 .card-progress-bar {
  height: 24px;
  border-radius: 14px;
}
.card-progress-section.v5 .card-progress-inner {
  height: 24px;
  border-radius: 14px;
}

.banner5-content-inner {
  max-width: 670px;
  margin: auto;
}

.banner5-timer h6 {
  text-transform: uppercase;
  font-size: 18px;
}

.timer-v5 li {
  color: #fff;
  font-family: "Bebas Neue";
  font-size: 50px;
  font-weight: 400;
  background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0.2) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
}
.timer-v5 li span {
  font-size: 50px;
  font-weight: 400;
}
.timer-v5 li::after {
  color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.4);
}

.buy-btn {
  margin-top: 3%;
}
.buy-btn.v5 {
  color: #111;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 30px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.banner-bottom-wrapper {
  padding: 70px 0;
  overflow: hidden;
  position: relative;
  z-index: 0;
}
.banner-bottom-wrapper::before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 70px;
  left: -10px;
  width: 110%;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  transform: rotate(4deg);
}
.banner-bottom-wrapper .banner-bottom-scrollar {
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  width: 110%;
  height: 80px;
  position: relative;
  z-index: 1;
  transform: rotate(-4deg);
  right: 10px;
  top: 0px;
}
.banner-bottom-wrapper .banner-bottom-scrollar-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  overflow: hidden;
}
.banner-bottom-wrapper .banner-bottom-scrollar-inner ul {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  margin: 0;
  list-style: none;
  padding: 0;
  animation: smoothSlider 25s infinite linear;
}
.banner-bottom-wrapper .banner-bottom-scrollar-inner ul li {
  color: #111;
  font-family: "Bebas Neue";
  font-size: 60px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  min-width: -moz-max-content;
  min-width: max-content;
}

@media only screen and (max-width: 991px) {
  .banner5-content h1 {
    font-size: 70px;
  }
  .banner-bottom-shape.v5 {
    height: 55px !important;
  }
}
@media only screen and (max-width: 768px) {
  .banner5-content h1 {
    font-size: 60px;
    line-height: 120%;
  }
  .banner-bottom-wrapper {
    padding: 60px 0;
  }
  .banner-bottom-wrapper::before {
    height: 40px;
  }
  .banner-bottom-wrapper .banner-bottom-scrollar {
    height: 40px;
  }
  .banner-bottom-wrapper .banner-bottom-scrollar-inner {
    gap: 20px;
  }
  .banner-bottom-wrapper .banner-bottom-scrollar-inner ul {
    gap: 20px;
    animation: smoothSlider 25s infinite linear;
  }
  .banner-bottom-wrapper .banner-bottom-scrollar-inner ul li {
    font-size: 25px;
    gap: 20px;
  }
  .banner-bottom-wrapper .banner-bottom-scrollar-inner ul li img {
    width: 25px;
  }
}
@media only screen and (max-width: 575px) {
  .banner5-section {
    padding: 120px 0 0px 0;
  }
  .banner5-content h1 {
    font-size: 40px;
  }
  .timer-v5 li {
    font-size: 36px;
  }
  .timer-v5 li span {
    font-size: 36px;
  }
  .card-progress-section.v5 h5 {
    margin-bottom: 20px;
    flex-direction: column;
    gap: 10px;
  }
}
.banner6-section {
  background: url("../images/bg/banner6-bg.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  min-height: 100vh;
  padding: 223px 0 60px 0;
  position: relative;
  z-index: 0;
  overflow: hidden;
}

.banner6-content {
  max-width: 742px;
  margin: auto;
  position: relative;
  z-index: 2;
}
.banner6-content h1 {
  color: #fff;
  font-family: "Bebas Neue";
  font-size: 90px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  margin-bottom: 20px;
}
.banner6-content h1 span {
  background: linear-gradient(180deg, #fff 0%, #bcff7b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner6-content p {
  font-size: 18px;
}

.banner6-card-section {
  position: relative;
  max-width: 570px;
  margin: auto;
}
.banner6-card-section::after {
  content: "";
  position: absolute;
  right: -22%;
  bottom: 10%;
  transform: rotate(210deg);
  height: 65%;
  width: 45%;
  border-radius: 20px;
  background: linear-gradient(180deg, rgba(82, 79, 31, 0) 0%, #1d200f 100%);
  -webkit-backdrop-filter: blur(15px);
          backdrop-filter: blur(15px);
}
.banner6-card-section::before {
  content: "";
  position: absolute;
  left: -22%;
  bottom: 10%;
  transform: rotate(-30deg);
  height: 65%;
  width: 45%;
  border-radius: 20px;
  background: linear-gradient(180deg, #052117 0%, rgba(32, 93, 55, 0) 100%);
  -webkit-backdrop-filter: blur(15px);
          backdrop-filter: blur(15px);
}

.banner6-card {
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(9, 43, 30, 0.9);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 30px 40px 40px 40px;
}

.v6-timer {
  margin: 0px -40px;
  padding: 15px 40px 10px 40px;
  background: rgba(255, 255, 255, 0.1);
  margin-top: 20px;
}

.card-progress-section.v6 {
  margin-bottom: 0;
  padding-bottom: 5px;
}
.card-progress-section.v6 h5 span {
  margin-bottom: 0;
}
.card-progress-section.v6 .card-progress-bar {
  height: 24px;
  border-radius: 14px;
}
.card-progress-section.v6 .card-progress-inner {
  height: 24px;
  border-radius: 14px;
}

.timer-v6 li {
  color: #fff;
  text-align: center;
  font-family: "Bebas Neue";
  font-size: 40px;
  font-weight: 400;
  background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0.2) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
}
.timer-v6 li span {
  font-size: 40px;
  font-weight: 400;
}
.timer-v6 li::after {
  color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.4);
}

.buy-btn {
  margin-top: 25px;
}
.buy-btn.v6 {
  color: #111;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 10px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.banner-bottom-wrapper.v6 {
  position: absolute;
  z-index: -2 !important;
  bottom: 269px;
  left: 0;
  width: 100%;
  padding: 102px 0;
  overflow: hidden;
  z-index: 0;
}
.banner-bottom-wrapper.v6::before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 110px;
  left: -10px;
  width: 110%;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  transform: rotate(6deg);
}
.banner-bottom-wrapper.v6 .banner-bottom-scrollar {
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  width: 110%;
  height: 80px;
  position: relative;
  z-index: 1;
  transform: rotate(-6deg);
  right: 10px;
  top: -10px;
}
.banner-bottom-wrapper.v6 .banner-bottom-scrollar-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  overflow: hidden;
}
.banner-bottom-wrapper.v6 .banner-bottom-scrollar-inner ul {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  margin: 0;
  list-style: none;
  padding: 0;
  animation: smoothSlider 25s infinite linear;
}
.banner-bottom-wrapper.v6 .banner-bottom-scrollar-inner ul li {
  color: #111;
  font-family: "Bebas Neue";
  font-size: 60px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 27px;
  min-width: -moz-max-content;
  min-width: max-content;
}

@media only screen and (max-width: 768px) {
  .banner6-content h1 {
    font-size: 70px;
  }
}
@media only screen and (max-width: 575px) {
  .banner6-section {
    padding: 120px 0 60px 0;
  }
  .v6-timer {
    margin: 0px -25px;
  }
  .timer-v6 li {
    font-size: 30px;
  }
  .timer-v6 li span {
    font-size: 30px;
  }
  .card-progress-section.v6 h5 {
    flex-direction: column;
    justify-content: center;
    margin-bottom: 15px;
    gap: 10px;
  }
  .banner6-card {
    padding: 20px 25px;
  }
  .banner6-content h1 {
    font-size: 48px;
  }
}
.banner7-section {
  background: url("../images/bg/banner7-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  position: relative;
  z-index: 0;
}

.banner-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.banner7-inner {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.banner7-inner .banner7-left {
  width: 60%;
}
.banner7-inner .banner7-right {
  width: 40%;
}

.banner7-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 25px;
  padding-bottom: 25px;
  margin-bottom: 70px;
}

.banner7-left {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.banner7-left-top-content {
  padding-right: 30px;
  padding-bottom: 63px;
}
.banner7-left-top-content h1 {
  color: #fff;
  font-family: Kufam;
  font-size: 70px;
  font-weight: 700;
  line-height: 128.571%;
  margin-bottom: 25px;
}
.banner7-left-top-content h1 span {
  background: linear-gradient(180deg, #fff 0%, #1dff96 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner7-left-top-content p {
  color: #fff;
  font-family: Outfit;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 166.667%;
  max-width: 536px;
}

.banner7-right-top-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.banner7-right-top-content ul li a,
.banner7-right-top-content ul li button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25px 0px 26px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: 0.3s;
  position: relative;
}
.banner7-right-top-content ul li a img,
.banner7-right-top-content ul li button img {
  transition: 0.3s;
}
.banner7-right-top-content ul li a span,
.banner7-right-top-content ul li button span {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 162.5%;
  transition: 0.3s;
}
.banner7-right-top-content ul li a span.justify-end,
.banner7-right-top-content ul li button span.justify-end {
  justify-content: flex-end;
}
.banner7-right-top-content ul li a span.w-50,
.banner7-right-top-content ul li button span.w-50 {
  width: 50%;
}
.banner7-right-top-content ul li a span.w-45,
.banner7-right-top-content ul li button span.w-45 {
  width: 45%;
}
.banner7-right-top-content ul li a strong,
.banner7-right-top-content ul li button strong {
  width: 5%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.banner7-right-top-content ul li a::before,
.banner7-right-top-content ul li button::before {
  position: absolute;
  content: "";
  bottom: -1px;
  left: 0px;
  width: 0%;
  height: 1px;
  background-color: rgb(30, 232, 183);
  transition: 0.3s;
}
.banner7-right-top-content ul li a:hover::before,
.banner7-right-top-content ul li button:hover::before {
  width: 100%;
}
.banner7-right-top-content ul li a:hover span,
.banner7-right-top-content ul li button:hover span {
  color: #fff;
}
.banner7-right-top-content ul li a:hover img,
.banner7-right-top-content ul li button:hover img {
  filter: brightness(100%);
}

.v7-progress-section {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: none;
  padding-right: 30px;
}

.card-progress-section.v7 h5 span {
  margin-bottom: 5px;
  font-size: 16px;
}
.card-progress-section.v7 .card-progress-bar {
  border-radius: 0;
}
.card-progress-section.v7 .card-progress-inner {
  border-radius: 0;
  background: #1dff96;
  width: 42%;
}

.banner7-timer {
  padding-left: 30px;
  padding-top: 14px;
  padding-bottom: 2px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.timer-v7 {
  justify-content: flex-start;
}
.timer-v7 li {
  color: #fff;
  font-family: "Bebas Neue";
  font-size: 40px;
  font-weight: 400;
  background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0.2) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
}
.timer-v7 li span {
  font-size: 40px;
  font-weight: 400;
}
.timer-v7 li::after {
  color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.4);
}

.banner7-token-content {
  padding: 10px 30px 30px 0px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.banner7-token-detail {
  padding-left: 30px;
  padding-top: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.banner7-token-detail ul {
  list-style: none;
  padding: 0;
  margin: auto;
}
.banner7-token-detail ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0px;
}

.buy-btn {
  margin-top: 25px;
}
.buy-btn.v7 {
  color: #111;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 0px;
  background: linear-gradient(90deg, #1dff96 0%, #bcff7b 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.banner7-bottom-left {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.banner7-bottom-left-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap-reverse;
  gap: 50px;
  padding: 30px 30px 30px 0px;
}
.banner7-bottom-left-content p {
  max-width: 204px;
}

.banner7-bottom-right-content {
  padding-top: 30px;
  padding-left: 30px;
}

.banner7-bottom p {
  color: #fff;
  font-family: Outfit;
  font-size: 18px;
  font-weight: 600;
  line-height: 166.667%;
  margin-bottom: 0;
}

@media only screen and (max-width: 1199px) {
  .banner7-left-top-content h1 {
    font-size: 60px;
  }
  .banner7-timer {
    padding-top: 22px;
  }
  .banner7-token-detail ul li {
    padding: 14px 0px;
  }
}
@media only screen and (max-width: 991px) {
  .banner-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
  .banner-wrapper .banner-item:nth-child(1) {
    order: 1;
  }
  .banner-wrapper .banner-item:nth-child(2) {
    order: 6;
  }
  .banner-wrapper .banner-item:nth-child(3) {
    order: 3;
  }
  .banner-wrapper .banner-item:nth-child(4) {
    order: 2;
  }
  .banner-wrapper .banner-item:nth-child(5) {
    order: 4;
  }
  .banner-wrapper .banner-item:nth-child(6) {
    order: 5;
  }
  .banner-wrapper .banner-item:nth-child(7) {
    order: 7;
  }
  .banner-wrapper .banner-item:nth-child(8) {
    order: 8;
  }
  .banner7-inner {
    flex-wrap: wrap;
  }
  .banner7-inner .banner7-left,
  .banner7-inner .banner7-right {
    width: 100%;
    border: none;
    padding-right: 0;
  }
  .banner7-left {
    border-right: 0;
  }
  .banner7-token-content {
    padding: 10px 0px 30px 0px;
    border-right: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  .banner7-right-top-content ul li a,
  .banner7-right-top-content ul li button {
    padding-left: 0;
  }
  .banner7-token-detail {
    padding-left: 0;
  }
  .banner7-left-top-content {
    border-right: none;
    padding-right: 0;
  }
  .v7-progress-section {
    border: none;
    padding: 0;
  }
  .banner7-timer {
    padding-top: 0;
    padding-left: 0;
  }
  .banner7-bottom-left {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  .banner7-bottom-left-content {
    padding-right: 0;
  }
  .banner7-bottom-right-content {
    padding-left: 0;
  }
  .banner7-bottom {
    padding-bottom: 40px;
  }
}
@media only screen and (max-width: 768px) {
  .banner7-header {
    margin-bottom: 40px;
  }
  .banner7-left-top-content {
    padding-bottom: 20px;
  }
  .banner7-left-top-content h1 {
    font-size: 60px;
    margin-bottom: 15px;
  }
}
@media only screen and (max-width: 575px) {
  .banner7-left-top-content {
    text-align: center;
    padding-bottom: 10px;
  }
  .banner7-left-top-content h1 {
    font-size: 40px;
  }
  .banner7-right-top-content ul li a,
  .banner7-right-top-content ul li button {
    padding: 15px 0px;
  }
  .banner7-token-detail {
    padding-top: 0;
  }
  .banner7-token-detail ul li {
    padding: 7px 0px;
  }
  .banner7-bottom-left-content {
    gap: 20px;
    justify-content: center;
    flex-direction: column-reverse;
  }
  .banner7-bottom-right-content {
    text-align: center;
    padding-top: 20px;
  }
}
.banner8-section {
  background: url("../images/bg/banner8-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  padding: 223px 0 0 0;
  position: relative;
  z-index: 0;
}

.banner8-left .banner-tag {
  border-radius: 19px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 9px;
  padding: 7px;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  max-width: 364px;
  margin-bottom: 30px;
}
.banner8-left .banner-tag span {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(103deg, #3c38ff 0%, #8c45ff 33.33%, #ff36c7 68.23%, #ffa336 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 2px 6px;
  line-height: 120%;
}
.banner8-left h1 {
  max-width: 488px;
  width: 100%;
  color: #fff;
  font-family: Kufam;
  font-size: 70px;
  font-style: normal;
  font-weight: 700;
  line-height: 128.571%;
  text-transform: uppercase;
  margin-bottom: 20px;
}
.banner8-left p {
  color: #fff;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 187.5%;
  max-width: 536px;
  margin-bottom: 30px;
}
.banner8-left .whitepaper-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(7.5px);
          backdrop-filter: blur(7.5px);
  width: 220px;
  height: 60px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  line-height: 162.5%;
}

.banner8-progress-card {
  border-radius: 40px 40px 0px 0px;
  background: linear-gradient(180deg, rgba(43, 46, 82, 0.95) 0%, rgba(43, 46, 82, 0) 100%);
  padding: 40px 40px 0px 40px;
  max-width: 430px;
  margin-left: auto;
  border-bottom: 0;
}

.banner8-progress {
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-progress {
  width: 350px;
  height: 350px;
  position: relative;
}
.circle-progress .progressbar-svg {
  transform: rotate(-87deg);
}
.circle-progress .progressbar-svg .progressbar-bg,
.circle-progress .progressbar-svg .progressbar-bg2 {
  fill: none;
  stroke: rgba(255, 255, 255, 0.05);
  stroke-width: 20px;
}
.circle-progress .progressbar-svg .progress-done,
.circle-progress .progressbar-svg .progress-done2 {
  fill: none;
  stroke: #3c38ff;
  stroke-width: 20px;
  stroke-dasharray: 923;
  stroke-dashoffset: calc(923 - 923 * var(--percent) / 100);
}
.circle-progress .progressbar-svg .progressbar-bg2,
.circle-progress .progressbar-svg .progress-done2 {
  display: none;
}
.circle-progress .progressbar-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(0deg);
}
.circle-progress .progressbar-inner img {
  animation: moveImg 10s linear infinite;
  width: 250px;
  height: 250px;
}
.circle-progress .progress-level {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.circle-progress .progress-level h2 {
  color: #ffffff;
  font-family: "Kufam", sans-serif;
  font-size: 50px;
  font-weight: 700;
  line-height: 1;
  text-transform: uppercase;
}

@keyframes moveImg {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.progress-card-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 50px;
  gap: 25px;
}
.progress-card-bottom p {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  line-height: 133.333%;
}

.presale-stage-section {
  position: relative;
  margin-top: 110px;
}
.presale-stage-section::before {
  position: absolute;
  content: "";
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 50px);
  height: 100%;
  border-radius: 40px 40px 0px 0px;
  background: linear-gradient(90deg, rgba(60, 56, 255, 0.4) 0%, rgba(140, 69, 255, 0.4) 36.98%, rgba(255, 54, 199, 0.4) 68.23%, rgba(255, 163, 54, 0.4) 100%);
  filter: blur(25px);
}
.presale-stage-section .presale-stage-tag {
  border-radius: 20px;
  background: linear-gradient(103deg, #3c38ff 0%, #8c45ff 33.33%, #ff36c7 68.23%, #ffa336 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  font-size: 18px;
  font-family: Outfit;
  color: #fff;
  position: absolute;
  top: -20px;
  left: 40px;
  padding: 5px 20px;
}

.presale-stage-content {
  border-radius: 40px 40px 0px 0px;
  border: 2px solid #fff;
  background: rgba(43, 46, 82, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.05);
  border-bottom: none;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.presale-stage-content .presale-stage-left {
  width: 50%;
  position: relative;
  padding-top: 40px;
}
.presale-stage-content .presale-stage-left::after {
  content: "";
  height: 100%;
  width: 2px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  position: absolute;
  top: 0;
  right: 0;
}

.presale-stage-right {
  width: 50%;
  padding: 40px;
}

.v8-timer {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  padding: 10px 40px;
}

.timer-v8 {
  justify-content: flex-start;
}
.timer-v8 li {
  color: #fff;
  text-align: center;
  font-family: "Bebas Neue";
  font-size: 40px;
  font-weight: 400;
  background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0.2) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
}
.timer-v8 li span {
  font-size: 40px;
  font-weight: 400;
}
.timer-v8 li::after {
  color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.4);
}

.banner8-token-detail ul {
  list-style: none;
  padding: 0;
  margin: auto;
}
.banner8-token-detail ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 40px;
  position: relative;
}
.banner8-token-detail ul li::after {
  content: "";
  height: 2px;
  width: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  bottom: 0;
  left: 0;
  position: absolute;
}
.banner8-token-detail ul li:nth-last-child(1)::after {
  display: none;
}
.banner8-token-detail ul li span {
  color: rgba(255, 255, 255, 0.8);
}
.banner8-token-detail ul li strong {
  color: #fff;
}

.buy-btn {
  margin-top: 25px;
}
.buy-btn.v8 {
  color: #fff;
  font-family: Outfit;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 162.5%;
  text-transform: uppercase;
  border-radius: 30px;
  background: linear-gradient(90deg, #3c38ff 0%, #8c45ff 33.33%, #ff36c7 68.23%, #ffa336 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

@media only screen and (max-width: 991px) {
  .banner8-progress-card {
    margin: auto;
  }
  .banner8-left {
    text-align: center;
    padding-bottom: 50px;
  }
  .banner8-left .banner-tag {
    margin: auto;
    margin-bottom: 25px;
  }
  .banner8-left h1 {
    margin: auto;
    margin-bottom: 10px;
  }
  .banner8-left p {
    margin: auto;
    margin-bottom: 30px;
  }
  .banner8-left .whitepaper-btn {
    margin: auto;
  }
  .v8-timer {
    margin-top: 5px;
  }
  .presale-stage-content {
    flex-wrap: wrap;
  }
  .presale-stage-content .presale-stage-left {
    width: 100%;
  }
  .presale-stage-content .presale-stage-left::after {
    display: none;
  }
  .presale-stage-right {
    width: 100%;
    padding-top: 10px;
  }
}
@media only screen and (max-width: 768px) {
  .banner8-left h1 {
    font-size: 60px;
  }
}
@media only screen and (max-width: 575px) {
  .banner8-section {
    padding: 120px 0 0 0;
  }
  .banner8-left h1 {
    font-size: 40px;
  }
  .banner8-left .banner-tag {
    font-size: 15px;
  }
  .v8-timer {
    padding: 10px 25px;
  }
  .timer-v8 li {
    font-size: 32px;
  }
  .timer-v8 li span {
    font-size: 32px;
  }
  .banner8-token-detail ul li {
    padding: 10px 25px;
  }
  .presale-stage-right {
    padding: 10px 25px 25px 25px;
  }
}
@media only screen and (max-width: 480px) {
  .circle-progress {
    width: 280px;
    height: 280px;
  }
  .circle-progress .progressbar-svg {
    transform: rotate(-87deg);
    width: 280px;
    height: 280px;
  }
  .circle-progress .progressbar-svg .progressbar-bg,
  .circle-progress .progressbar-svg .progress-done {
    display: none;
  }
  .circle-progress .progressbar-svg .progressbar-bg2,
  .circle-progress .progressbar-svg .progress-done2 {
    display: block;
  }
  .circle-progress .progressbar-svg .progress-done2 {
    stroke-dasharray: 703;
    stroke-dashoffset: calc(703 - 703 * var(--percent) / 100);
  }
  .circle-progress .progressbar-inner img {
    width: 180px;
    height: 180px;
  }
  .circle-progress .progress-level h2 {
    font-size: 40px;
  }
}
.banner9-section {
  background: url("../images/bg/banner9-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  padding: 223px 0 170px 0;
  position: relative;
  z-index: 0;
}
.banner9-section::before {
  z-index: -1;
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(14, 17, 23, 0.6980392157);
}

.banner9-left {
  padding-top: 50px;
}
.banner9-left h1 {
  font-weight: 700;
  font-size: 60px;
  line-height: 133%;
  color: #ffffff;
  margin-bottom: 12px;
}
.banner9-left p {
  margin-bottom: 39px;
  font-weight: 500;
  font-size: 18px;
  line-height: 200%;
  color: #ffffff;
}
.banner9-left button {
  font-weight: 700;
  font-size: 15px;
  line-height: 26px;
  text-transform: uppercase;
  color: #ffffff;
  height: 60px;
  width: 220px;
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(7.5px);
          backdrop-filter: blur(7.5px);
  border-radius: 30px;
  margin-bottom: 41px;
}
.banner9-left button span {
  margin-right: 9.5px;
}

.banner-list {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
.banner-list.v2 {
  margin-top: 40px;
}
.banner-list li {
  font-weight: 500;
  font-size: 18px;
  line-height: 222%;
  color: rgba(255, 255, 255, 0.8);
}
.banner-list li span {
  color: #ffffff;
  margin-left: 10px;
}

.banner9-right {
  perspective: 1000px;
  position: relative;
  width: 512px;
  max-width: 100%;
  margin-left: auto;
}

.banner9-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border-radius: 20px;
  transform-style: preserve-3d;
  transform: rotateY(0deg);
  transition: 0.6s;
}
.banner9-card.flip {
  transform: rotateY(180deg);
}
.banner9-card.flip .card-content {
  display: none;
}
.banner9-card.flip .card-content2 {
  display: block;
  transform: rotateY(180deg);
}
.banner9-card .card-content {
  position: relative;
  z-index: 1;
  padding: 21px 40px 40px 40px;
}
.banner9-card .card-content::after {
  content: url("../images/shape/shape1.png");
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -1;
}
.banner9-card .card-content::before {
  content: url("../images/shape/shape2.png");
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: -1;
}
.banner9-card button {
  background: #1ee8b7;
  border-radius: 30px;
  width: 100%;
  height: 60px;
  font-weight: 700;
  font-size: 15px;
  line-height: 173%;
  text-transform: uppercase;
  color: #0e1117;
}

.banner9-timer .presale-end-time {
  justify-content: flex-start;
}

.card-progress-section {
  margin-top: 20px;
  padding-bottom: 10px;
}
.card-progress-section h5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.card-progress-section h5 span {
  margin-bottom: 13px;
}

.card-content p {
  margin-left: 39px;
}

.card-progress-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 16px;
}

.card-progress-inner {
  background: #1ee8b7;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 700;
  font-size: 14px;
  line-height: 15px;
  color: #111111;
  width: 23%;
  padding: 2px 5px;
}

.card-info {
  margin-bottom: 35px;
}
.card-info ul {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
.card-info ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  padding: 10px 0px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-content2 {
  display: none;
  z-index: 1;
  position: relative;
  padding: 21px 40px 40px 40px;
}
.card-content2::after {
  content: url(../images/shape/shape1.png);
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -1;
}
.card-content2::before {
  content: url(../images/shape/shape2.png);
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: -1;
}
.card-content2 button {
  margin-top: 10px;
}
.card-content2 h6 {
  margin-bottom: 26px;
}
.card-content2 .back-btn {
  margin-left: 40px;
  cursor: pointer;
}
.card-content2 form {
  margin-top: 30px;
}

.input-section label {
  font-weight: 500;
  font-size: 15px;
  line-height: 200%;
  text-transform: uppercase;
  color: #ffffff;
  margin-bottom: 6px;
  font-family: "Outfit", sans-serif;
}
.input-section input {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 18px;
  line-height: 100%;
  color: #ffffff;
  font-family: "Inter", sans-serif;
  max-width: 100%;
  height: 60px;
  width: 100%;
  overflow: hidden;
  margin-bottom: 24px;
}

.card-shape {
  position: absolute;
  top: -64px;
  left: -64px;
  height: 130px;
  width: 130px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(98, 54, 255, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 11;
}
.card-shape .card-shape-inner {
  position: relative;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-shape .card-shape-inner img {
  animation: rotate360 15s infinite linear;
}
.card-shape .card-shape-inner img.live-arrow {
  animation: none;
}
.card-shape .card-shape-inner .live-arrow {
  position: absolute;
  transform: rotate(-135deg);
}

.banner9-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.0509803922);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.banner9-footer ul {
  list-style: none;
  padding: 0px;
  margin: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26px;
  animation: smoothSlider 25s infinite linear;
}
.banner9-footer ul li {
  margin-right: 120px;
  min-width: 120px;
}

@media only screen and (max-width: 1199px) {
  .banner9-left h1 {
    font-size: 52px;
  }
  .banner9-left p {
    margin-bottom: 20px;
  }
  .banner9-left button {
    margin-bottom: 20px;
  }
  .banner9-section .overlay2 {
    padding-bottom: 150px;
  }
}
@media only screen and (max-width: 991px) {
  .banner9-section {
    padding: 160px 0 170px 0;
  }
  .banner9-section .overlay2 {
    padding-top: 180px;
  }
  .banner9-left {
    max-width: 430px;
    padding-top: 0px;
    margin: auto;
    margin-bottom: 120px;
    text-align: center;
  }
  .banner9-right {
    margin-left: 0px;
    padding-left: 65px;
  }
  .card-shape {
    left: 0;
  }
}
@media only screen and (max-width: 768px) {
  .banner9-section .overlay2 {
    padding-top: 150px;
  }
  .card-progress-section h5 {
    flex-wrap: wrap;
  }
  .card-info ul li {
    flex-wrap: wrap;
  }
}
@media only screen and (max-width: 575px) {
  .banner9-section {
    padding: 120px 0 170px 0;
  }
  .progress-section.v1 .progress-top-text {
    justify-content: center;
    flex-direction: column;
  }
  .card-content,
  .card-content2 {
    padding: 20px;
  }
  .main-content-progress-section h5 {
    flex-wrap: wrap;
  }
  .banner9-left h1 {
    font-size: 40px;
  }
  .banner9-right {
    padding-left: 0px;
  }
  .card-shape {
    top: -65px;
    width: 90px;
    height: 90px;
  }
  .card-shape .card-shape-inner {
    padding: 10px;
  }
}
@media only screen and (max-width: 425px) {
  .banner9-card .card-content {
    padding: 20px 20px 30px 20px;
  }
}
.banner10-section {
  background: url("../images/bg/banner10-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  padding: 182px 0 60px 0;
  position: relative;
  z-index: 0;
}
.banner10-section::before {
  z-index: -1;
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(5, 5, 5, 0.9019607843);
}
.banner10-section::after {
  z-index: -1;
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(5, 5, 5, 0.1607843137) 0%, rgba(45, 5, 94, 0.5607843137) 77.08%, rgba(42, 26, 227, 0.8) 100%);
}

.banner10-content h1 {
  font-weight: 700;
  font-size: 70px;
  line-height: 129%;
  text-align: center;
  color: #ffffff;
  max-width: 888px;
  margin: auto;
  text-align: center;
  margin-bottom: 41px;
}
.banner10-content h5 {
  max-width: 501px;
  width: 100%;
  text-align: center;
  margin: auto;
  margin-bottom: 40px;
}

.v10-timer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.v10-timer ul {
  justify-content: flex-end;
}
.v10-timer ul li {
  color: #fff;
  font-family: Outfit;
  font-size: 30px;
}
.v10-timer ul li::after {
  color: rgba(255, 255, 255, 0.2);
}
.v10-timer ul li span {
  font-size: 18px;
}

.main-content {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.main-content-left {
  background: rgba(255, 255, 255, 0.05);
  width: 50%;
}

.sale-ends-timer {
  padding: 30px 40px;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
}

.main-content-info {
  padding: 21px 40px 40px 40px;
}

.main-content-progress-section h5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.main-content-progress-section h5 span {
  margin-bottom: 13px;
}

.main-content-progress-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 13px;
}

.main-content-progress-inner {
  background: #6acf15;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 700;
  font-size: 12px;
  line-height: 15px;
  color: #ffffff;
  width: 23%;
  padding: 2px 5px;
}

.main-content-info ul {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
.main-content-info ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  padding: 10px 0px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-content-right {
  width: 50%;
  padding: 40px;
}
.main-content-right h5 {
  margin-bottom: 21px;
}
.main-content-right button {
  background: conic-gradient(from 0deg at 50% 50%, #6acf15 0deg, #209b44 360deg);
  border-radius: 50px;
  max-width: 245px;
  width: 100%;
  height: 60px;
  font-weight: 700;
  font-size: 15px;
  line-height: 173%;
  text-transform: uppercase;
  color: #ffffff;
}

@media only screen and (max-width: 1199px) {
  .main-content-right {
    padding: 30px 20px;
  }
}
@media only screen and (max-width: 991px) {
  .banner10-content h1 {
    font-size: 55px;
  }
  .main-content {
    flex-direction: column;
  }
  .main-content-left {
    width: 100%;
  }
  .main-content-right {
    width: 100%;
  }
}
@media only screen and (max-width: 768px) {
  .banner10-content h1 {
    font-size: 36px;
  }
  .banner10-content h2 {
    max-width: 390px;
  }
}
@media only screen and (max-width: 575px) {
  .banner10-section {
    padding: 120px 0 60px 0;
  }
  .sale-ends-timer {
    flex-wrap: wrap;
  }
  .main-content-right {
    padding: 30px 20px;
  }
  .token-dropdown.v10 {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 480px) {
  .main-content-info {
    padding: 20px;
  }
  .sale-ends-timer {
    padding: 20px;
  }
  .main-content-right {
    padding: 20px;
  }
}
/*--------------------------------
06. Footer Section
--------------------------------*/
.footer-section {
  background: url("../images/footer/bg_footer.html");
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
}
.footer-section .overlay {
  height: 100%;
  width: 100%;
  background: linear-gradient(180deg, #12151f 0%, rgba(18, 21, 31, 0.9) 100%);
  border-radius: 0px;
}

.footer-card {
  background: linear-gradient(104.92deg, rgba(0, 227, 124, 0.1) 0%, rgba(0, 227, 124, 0) 60.52%);
  border-radius: 30px;
}
.footer-card .social-links-section {
  width: 100%;
  max-height: 570px;
  margin-left: auto;
}
.footer-card .social-links-section ul {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 30px;
  flex-wrap: wrap;
}
.footer-card .social-links-section ul li {
  height: 90px;
  width: 90px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(2.5px);
          backdrop-filter: blur(2.5px);
  border-radius: 20px;
  overflow: hidden;
}
.footer-card .social-links-section ul li img {
  position: relative;
  z-index: 2;
}
.footer-card .social-links-section ul li::after {
  content: "";
  position: absolute;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 49.48%, rgba(255, 255, 255, 0) 100%);
  height: 50px;
  width: 1px;
  right: 0px;
}
.footer-card .social-links-section ul li::before {
  content: "";
  position: absolute;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 49.48%, rgba(255, 255, 255, 0) 100%);
  height: 50px;
  width: 1px;
  left: 0px;
}
.footer-card .social-links-section ul li:hover a::after {
  content: url("../images/social/social-hov-shape2.html");
  border-radius: 0px 20px 20px 0px;
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 0;
  height: 100%;
}
.footer-card .social-links-section ul li:hover a::before {
  content: url("../images/social/social-hov-shape1.html");
  border-radius: 20px 0px 0px 20px;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 0;
  height: 100%;
}
.footer-card p {
  max-width: 240px;
}

.footer-menu-list li {
  margin-bottom: 10px;
}
.footer-menu-list li:nth-last-child(1) {
  margin-bottom: 0px;
}

.footer-bottom-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.footer-bottom-section ul {
  max-width: 432px;
  width: 100%;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.footer-bottom-section ul li a:hover {
  color: #ffffff;
}

.back-to-top-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}
.back-to-top-btn:hover {
  color: #ffffff;
}