<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gittu - Swap</title>

  <!-- favicon included here -->
  <link rel="shortcut icon" href="assets/images/logo/favicon.ico" type="image/x-icon">
  <!-- apple touch icon included here -->
  <link rel="apple-touch-icon" href="assets/images/logo/favicon.ico">

  <!-- All css files included here -->
  <!-- Bootstrap v5.3.6 css -->
  <link rel="stylesheet" href="assets/css/bootstrap.min.css">
  <!-- custom css -->
  <link rel="stylesheet" href="assets/css/style.css">
  
  <style>
    /* Custom Swap Page Styles */
    .swap-section {
      background: linear-gradient(135deg, #1a1d29 0%, #2d3748 50%, #4a5568 100%);
      min-height: 100vh;
      padding: 120px 0;
      position: relative;
      overflow: hidden;
    }
    
    .swap-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%);
      pointer-events: none;
    }
    
    .swap-container {
      max-width: 480px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }
    
    .swap-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 24px;
      padding: 32px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }
    
    .swap-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32px;
    }
    
    .swap-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }
    
    .settings-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 12px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .settings-btn:hover {
      background: rgba(255, 255, 255, 0.15);
    }
    
    .token-input-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 8px;
    }
    
    .token-input-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .token-label {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      font-weight: 500;
    }
    
    .token-balance {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
    }
    
    .token-input-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .token-amount-input {
      background: transparent;
      border: none;
      outline: none;
      color: #ffffff;
      font-size: 28px;
      font-weight: 600;
      flex: 1;
      width: 100%;
    }
    
    .token-amount-input::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
    
    .token-selector {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 12px;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
    }
    
    .token-selector:hover {
      background: rgba(255, 255, 255, 0.15);
    }
    
    .token-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
    
    .token-symbol {
      color: #ffffff;
      font-weight: 600;
      font-size: 16px;
    }
    
    .dropdown-arrow {
      color: rgba(255, 255, 255, 0.7);
      font-size: 12px;
    }
    
    .swap-arrow-container {
      display: flex;
      justify-content: center;
      margin: 8px 0;
      position: relative;
    }
    
    .swap-arrow-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 4px solid #1a1d29;
      border-radius: 12px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      z-index: 3;
    }
    
    .swap-arrow-btn:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: rotate(180deg);
    }
    
    .swap-arrow-btn svg {
      color: #ffffff;
      width: 20px;
      height: 20px;
    }
    
    .exchange-rate {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 16px;
      margin: 24px 0;
    }
    
    .rate-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .rate-value {
      color: #ffffff;
      font-weight: 600;
    }
    
    .fee-info {
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      text-align: center;
    }
    
    .connect-wallet-btn-swap {
      background: linear-gradient(135deg, #00d4ff 0%, #1ee8b7 100%);
      border: none;
      border-radius: 16px;
      width: 100%;
      height: 56px;
      color: #0e1117;
      font-size: 18px;
      font-weight: 700;
      text-transform: uppercase;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 24px;
    }
    
    .connect-wallet-btn-swap:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(30, 232, 183, 0.3);
    }
    
    .connect-wallet-btn-swap:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.5);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    @media (max-width: 768px) {
      .swap-section {
        padding: 80px 20px;
      }
      
      .swap-card {
        padding: 24px;
      }
      
      .token-amount-input {
        font-size: 24px;
      }
    }
  </style>
</head>

<body>
  <!-- Header Section Start -->
  <header class="header-section header9">
    <div class="container">
      <div class="header-inner">
        <a href="index.html" class="logo">
          <img src="assets/images/logo/logo.svg" alt="logo">
        </a>

        <div class="header-right">
          <ul class="social-links">
            <li>
              <a href="https://telegram.org/" target="_blank">
                <img src="assets/images/icon/telegram.svg" alt="icon">
              </a>
            </li>
            <li>
              <a href="https://discord.com/" target="_blank">
                <img src="assets/images/icon/discord.svg" alt="icon">
              </a>
            </li>
            <li>
              <a href="https://x.com/" target="_blank">
                <img src="assets/images/icon/twitter.svg" alt="icon">
              </a>
            </li>
          </ul>

          <button type="button" class="connect-wallet-btn v9">
            Connect <span>Wallet</span>
          </button>

          <div class="menu-btn">
            <button type="button" class="demo-btn">
              <img src="assets/images/icon/menu.svg" alt="icon">
            </button>

            <ul class="demo-list">
              <li><a href="index.html">Demo One</a></li>
              <li><a href="index-2.html">Demo Two</a></li>
              <li><a href="index-3.html">Demo Three</a></li>
              <li><a href="index-4.html">Demo Four</a></li>
              <li><a href="index-5.html">Demo Five</a></li>
              <li><a href="index-6.html">Demo Six</a></li>
              <li><a href="index-7.html">Demo Seven</a></li>
              <li><a href="index-8.html">Demo Eight</a></li>
              <li><a href="index-9.html">Demo Nine</a></li>
              <li><a href="index-10.html">Demo Ten</a></li>
              <li><a class="active" href="swap.html">Swap</a></li>
            </ul>
          </div>

          <!-- mobile-menu -->
          <div class="mobile-menu">
            <button class="navbar-toggler mobile-menu-btn" type="button" data-bs-toggle="offcanvas"
              data-bs-target="#offcanvasNavbar" aria-controls="offcanvasNavbar" aria-label="Toggle navigation">
              <img src="assets/images/icon/menu-bar.svg" alt="icon">
            </button>

            <div class="offcanvas offcanvas-start mobile-menu-content" data-bs-scroll="true" tabindex="-1"
              id="offcanvasNavbar">
              <div class="offcanvas-header mobile-menu-top">
                <a href="index.html" class="logo">
                  <img src="assets/images/logo/logo.svg" alt="logo">
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close">
                  <img src="assets/images/icon/cross.svg" alt="icon">
                </button>
              </div>

              <div class="offcanvas-body">
                <ul class="mobile-menu-list">
                  <li><a href="index.html">Demo One</a></li>
                  <li><a href="index-2.html">Demo Two</a></li>
                  <li><a href="index-3.html">Demo Three</a></li>
                  <li><a href="index-4.html">Demo Four</a></li>
                  <li><a href="index-5.html">Demo Five</a></li>
                  <li><a href="index-6.html">Demo Six</a></li>
                  <li><a href="index-7.html">Demo Seven</a></li>
                  <li><a href="index-8.html">Demo Eight</a></li>
                  <li><a href="index-9.html">Demo Nine</a></li>
                  <li><a href="index-10.html">Demo Ten</a></li>
                  <li><a class="active" href="swap.html">Swap</a></li>
                </ul>

                <ul class="mobile-social-links">
                  <li>
                    <a href="https://telegram.org/" target="_blank">
                      <img src="assets/images/icon/telegram.svg" alt="">
                    </a>
                  </li>
                  <li>
                    <a href="https://discord.com/" target="_blank">
                      <img src="assets/images/icon/discord.svg" alt="">
                    </a>
                  </li>
                  <li>
                    <a href="https://x.com/" target="_blank">
                      <img src="assets/images/icon/twitter.svg" alt="">
                    </a>
                  </li>
                </ul>

                <ul class="mobile-menu-list">
                  <li>
                    <a class="whitepaper-btn-mobile" target="_blank" aria-current="page"
                      href="assets/pdf/whitepaper.pdf">
                      Whitepaper
                    </a>
                  </li>
                </ul>

                <div class="connect-btn">
                  <button type="button" class="connect-wallet-btn">
                    Connect <span>Wallet</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- Header Section End -->

  <!-- Swap Section Start -->
  <section class="swap-section">
    <div class="container">
      <div class="swap-container">
        <div class="swap-card">
          <div class="swap-header">
            <h2 class="swap-title">Swap</h2>
            <button class="settings-btn" type="button">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <!-- From Token Section -->
          <div class="token-input-section">
            <div class="token-input-header">
              <span class="token-label">From</span>
              <span class="token-balance">Balance: 0</span>
            </div>
            <div class="token-input-content">
              <input type="text" class="token-amount-input" placeholder="0" value="0">
              <div class="token-selector" id="fromTokenSelector">
                <img src="assets/images/token/eth_logo.png" alt="ETH" class="token-icon">
                <span class="token-symbol">ETH</span>
                <span class="dropdown-arrow">▼</span>
              </div>
            </div>
          </div>

          <!-- Swap Arrow -->
          <div class="swap-arrow-container">
            <button class="swap-arrow-btn" type="button" id="swapTokens">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <!-- To Token Section -->
          <div class="token-input-section">
            <div class="token-input-header">
              <span class="token-label">To</span>
              <span class="token-balance">Balance: 0</span>
            </div>
            <div class="token-input-content">
              <input type="text" class="token-amount-input" placeholder="0" value="0" readonly>
              <div class="token-selector" id="toTokenSelector">
                <img src="assets/images/token/token_usdt.svg" alt="USDC" class="token-icon">
                <span class="token-symbol">USDC</span>
                <span class="dropdown-arrow">▼</span>
              </div>
            </div>
          </div>

          <!-- Exchange Rate Info -->
          <div class="exchange-rate">
            <div class="rate-info">
              <span>1 ETH = 0.0006 USDC ($-)</span>
              <span class="rate-value">Fee ~ ETH</span>
            </div>
            <div class="fee-info">
              Fee includes network costs and may vary based on network congestion
            </div>
          </div>

          <!-- Connect Wallet Button -->
          <button class="connect-wallet-btn-swap" type="button" id="connectWalletSwap">
            Connect Wallet
          </button>
        </div>
      </div>
    </div>
  </section>
  <!-- Swap Section End -->

  <!-- connect modal -->
  <div class="connect-modal">
    <!-- connect modal -->
    <div class="modal fade" id="connectModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Connect a Wallet</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
              <img src="assets/images/icon/cross.svg" alt="icon">
            </button>
          </div>
          <div class="modal-body">
            <ul class="wallet-list">
              <li>
                <a class="gittu-connect-btn">
                  <img src="assets/images/wallet/metamask.svg" alt="metamask"><span
                    class="wt-700 kanit">MetaMask</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/formatic.png" alt="formatic">
                  <span class="wt-700 kanit">Formatic</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/trust-wallet.svg" alt="trust-wallet">
                  <span class="wt-700 kanit">Trust Wallet</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/wallet-connect.svg" alt="wallet-connect"><span
                    class="wt-700 kanit">WalletConnect</span>
                </a>
              </li>
              <li>
                <a href="#">
                  <img src="assets/images/wallet/phantom.png" alt="phantom">
                  <span class="wt-700 kanit">Phantom</span>
                </a>
              </li>
            </ul>
            <p class="text-center">
              By connecting your wallet, you agree to our
              <a href="#">Terms of Service</a> and our
              <a href="#">Privacy Policy</a>.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- disconnect modal -->
    <div class="modal fade" id="disconnectModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Disconnect wallet</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
              <img src="assets/images/icon/cross.svg" alt="icon">
            </button>
          </div>
          <div class="modal-body">
            <div class="gittu-wallet-info">
              <h4 class="wallet-address">01ax...24544</h4>
              <h4><span class="wallet-balance">0 ETH</span></h4>
            </div>

            <div class="action-buttons">
              <button class="gittu-copywallet-btn">
                <img src="assets/images/icon/copy.svg" alt="metamask">
                <span>Copy Address</span>
              </button>
              <button class="gittu-disconnect-btn">
                <img src="assets/images/icon/disconnect.svg" alt="metamask">
                <span>Disconnect</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- All javascript files included here -->
  <!-- jQuery v3.7.1 js -->
  <script src="assets/js/jquery.min.js"></script>
  <!-- Bootstrap v5.3.6 js -->
  <script src="assets/js/bootstrap.min.js"></script>
  <!-- modernizr js -->
  <script src="assets/js/modernizr.js"></script>
  <!-- connect wallet js -->
  <script src="assets/js/ethers.umd.min.js"></script>
  <script src="assets/js/connectWallet.js"></script>
  <!-- custom js -->
  <script src="assets/js/main.js"></script>

  <script>
    // Swap functionality
    document.addEventListener('DOMContentLoaded', function() {
      const swapButton = document.getElementById('swapTokens');
      const fromTokenSelector = document.getElementById('fromTokenSelector');
      const toTokenSelector = document.getElementById('toTokenSelector');
      const connectWalletBtn = document.getElementById('connectWalletSwap');

      // Swap tokens functionality
      swapButton.addEventListener('click', function() {
        const fromToken = fromTokenSelector.innerHTML;
        const toToken = toTokenSelector.innerHTML;

        fromTokenSelector.innerHTML = toToken;
        toTokenSelector.innerHTML = fromToken;
      });

      // Connect wallet functionality
      connectWalletBtn.addEventListener('click', function() {
        const connectModal = new bootstrap.Modal(document.getElementById('connectModal'));
        connectModal.show();
      });

      // Token amount input handling
      const fromAmountInput = document.querySelector('.token-input-section:first-of-type .token-amount-input');
      const toAmountInput = document.querySelector('.token-input-section:last-of-type .token-amount-input');

      fromAmountInput.addEventListener('input', function() {
        // Simple mock conversion rate (1 ETH = 2500 USDC)
        const amount = parseFloat(this.value) || 0;
        const convertedAmount = amount * 2500;
        toAmountInput.value = convertedAmount.toFixed(2);
      });
    });
  </script>

</body>
</html>
